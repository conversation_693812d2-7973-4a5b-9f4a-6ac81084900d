# MQTT网关安装验证脚本
Write-Host "=== MQTT网关安装验证 ===" -ForegroundColor Green

$InstallDir = "./install_test"
$BinDir = "$InstallDir/bin"
$ConfigDir = "$InstallDir/bin/config"
$ShareConfigDir = "$InstallDir/share/fstation/config"

Write-Host "`n✅ 安装验证结果:" -ForegroundColor Green

# 1. 可执行文件
$ExeFile = "$BinDir/MQTTGateway.exe"
if (Test-Path $ExeFile) {
    Write-Host "✓ MQTTGateway.exe 已安装" -ForegroundColor Green
} else {
    Write-Host "✗ MQTTGateway.exe 缺失" -ForegroundColor Red
}

# 2. Qt库
$QtLibs = @("Qt6Core.dll", "Qt6Network.dll")
foreach ($Lib in $QtLibs) {
    if (Test-Path "$BinDir/$Lib") {
        Write-Host "✓ $Lib 已安装" -ForegroundColor Green
    } else {
        Write-Host "✗ $Lib 缺失" -ForegroundColor Red
    }
}

# 3. Qt插件
if (Test-Path "$BinDir/platforms") {
    Write-Host "✓ Qt platforms插件已安装" -ForegroundColor Green
} else {
    Write-Host "✗ Qt platforms插件缺失" -ForegroundColor Red
}

# 4. MQTT库
$MqttLibs = @("paho-mqtt3as.dll", "paho-mqttpp3.dll")
foreach ($Lib in $MqttLibs) {
    if (Test-Path "$BinDir/$Lib") {
        Write-Host "✓ $Lib 已安装" -ForegroundColor Green
    } else {
        Write-Host "✗ $Lib 缺失" -ForegroundColor Red
    }
}

# 5. OpenSSL库
$SslLibs = @("libcrypto-3-x64.dll", "libssl-3-x64.dll")
foreach ($Lib in $SslLibs) {
    if (Test-Path "$BinDir/$Lib") {
        Write-Host "✓ $Lib 已安装" -ForegroundColor Green
    } else {
        Write-Host "✗ $Lib 缺失" -ForegroundColor Red
    }
}

# 6. 配置文件
$ConfigFiles = @(
    "gateway.json", "data_points.json", "events.json",
    "commands.json", "subscriptions.json", "publish_topics.json",
    "scada_ctrl_conf.json"
)
foreach ($File in $ConfigFiles) {
    if (Test-Path "$ConfigDir/$File") {
        Write-Host "✓ $File 已安装到程序目录" -ForegroundColor Green
    } else {
        Write-Host "✗ $File 在程序目录中缺失" -ForegroundColor Red
    }
}

# 7. 程序启动测试
Write-Host "`n🔧 程序启动测试:" -ForegroundColor Yellow
try {
    $Output = & "$ExeFile" --help 2>&1
    if ($Output -match "MQTT网关程序") {
        Write-Host "✓ 程序可以启动并显示信息" -ForegroundColor Green
    } else {
        Write-Host "⚠ 程序启动但可能有配置问题" -ForegroundColor Yellow
    }
} catch {
    Write-Host "✗ 程序无法启动: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n📁 安装目录结构:" -ForegroundColor Cyan
Write-Host "  $InstallDir/" -ForegroundColor White
Write-Host "  ├── bin/                    # 可执行文件和DLL" -ForegroundColor Gray
Write-Host "  │   ├── MQTTGateway.exe" -ForegroundColor Gray
Write-Host "  │   ├── *.dll               # 依赖库" -ForegroundColor Gray
Write-Host "  │   ├── platforms/          # Qt插件" -ForegroundColor Gray
Write-Host "  │   └── config/             # 程序配置文件" -ForegroundColor Gray
Write-Host "  └── share/fstation/config/  # 标准配置文件位置" -ForegroundColor Gray

Write-Host "`n🎯 总结:" -ForegroundColor Green
Write-Host "MQTT网关安装包已成功创建，包含所有必要的依赖库和配置文件。" -ForegroundColor White
Write-Host "程序可以启动，但可能需要根据实际环境调整配置文件。" -ForegroundColor White
