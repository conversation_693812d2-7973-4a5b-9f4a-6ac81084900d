# FStation 安装配置模块
# 定义安装组件和目录结构

include(GNUInstallDirs)

# 定义安装组件
set(FSTATION_INSTALL_COMPONENTS
    Runtime         # 运行时文件（可执行文件、DLL、配置文件）
    Development     # 开发文件（头文件、静态库、CMake配置）
    Documentation   # 文档文件
)

# 设置安装目录变量
set(FSTATION_INSTALL_BINDIR "${CMAKE_INSTALL_BINDIR}")
set(FSTATION_INSTALL_LIBDIR "${CMAKE_INSTALL_LIBDIR}")
set(FSTATION_INSTALL_INCLUDEDIR "${CMAKE_INSTALL_INCLUDEDIR}")
set(FSTATION_INSTALL_DATADIR "${CMAKE_INSTALL_DATADIR}/fstation")
set(FSTATION_INSTALL_CONFIGDIR "${FSTATION_INSTALL_DATADIR}/config")
set(FSTATION_INSTALL_DOCDIR "${CMAKE_INSTALL_DOCDIR}")

# 安装vcpkg依赖库DLL的函数
function(install_vcpkg_dlls)
    if(NOT WIN32)
        return()
    endif()

    # 查找vcpkg安装目录 - 优先使用本地安装
    set(VCPKG_INSTALLED_DIR "")
    # 1. 优先使用本地vcpkg_installed（项目特定）
    if(EXISTS "${CMAKE_SOURCE_DIR}/MQTTGateway/vcpkg_installed")
        set(VCPKG_INSTALLED_DIR "${CMAKE_SOURCE_DIR}/MQTTGateway/vcpkg_installed")
        message(STATUS "使用本地vcpkg安装目录: ${VCPKG_INSTALLED_DIR}")
    elseif(EXISTS "${CMAKE_SOURCE_DIR}/vcpkg_installed")
        set(VCPKG_INSTALLED_DIR "${CMAKE_SOURCE_DIR}/vcpkg_installed")
        message(STATUS "使用项目根vcpkg安装目录: ${VCPKG_INSTALLED_DIR}")
    # 2. 回退到全局vcpkg安装
    elseif(DEFINED ENV{VCPKG_ROOT})
        file(TO_CMAKE_PATH "$ENV{VCPKG_ROOT}/installed" VCPKG_INSTALLED_DIR)
        message(STATUS "使用全局vcpkg安装目录: ${VCPKG_INSTALLED_DIR}")
    endif()

    if(VCPKG_INSTALLED_DIR STREQUAL "")
        message(WARNING "无法找到vcpkg安装目录，跳过DLL安装")
        return()
    endif()

    # 检测实际的构建类型 - 与install_vcrt_dlls()保持一致
    set(ACTUAL_BUILD_TYPE "")

    # 调试信息
    message(STATUS "vcpkg DLL: CMAKE_INSTALL_CONFIG_NAME = '${CMAKE_INSTALL_CONFIG_NAME}'")
    message(STATUS "vcpkg DLL: CMAKE_BUILD_TYPE = '${CMAKE_BUILD_TYPE}'")
    message(STATUS "vcpkg DLL: CMAKE_CONFIGURATION_TYPES = '${CMAKE_CONFIGURATION_TYPES}'")

    # 1. 优先使用CMAKE_INSTALL_CONFIG_NAME（安装时指定的配置）
    if(DEFINED CMAKE_INSTALL_CONFIG_NAME AND NOT CMAKE_INSTALL_CONFIG_NAME STREQUAL "")
        set(ACTUAL_BUILD_TYPE "${CMAKE_INSTALL_CONFIG_NAME}")
        message(STATUS "vcpkg DLL: 使用安装时指定的配置: ${ACTUAL_BUILD_TYPE}")
    # 2. 其次使用CMAKE_BUILD_TYPE（单配置生成器）
    elseif(DEFINED CMAKE_BUILD_TYPE AND NOT CMAKE_BUILD_TYPE STREQUAL "")
        set(ACTUAL_BUILD_TYPE "${CMAKE_BUILD_TYPE}")
        message(STATUS "vcpkg DLL: 使用构建时指定的配置: ${ACTUAL_BUILD_TYPE}")
    # 3. 检查Release构建输出（优先检查Release，因为我们想测试Release）
    elseif(EXISTS "${CMAKE_BINARY_DIR}/MQTTGateway/Release/MQTTGateway.exe")
        set(ACTUAL_BUILD_TYPE "Release")
        message(STATUS "vcpkg DLL: 检测到Release构建输出，使用Release配置")
    # 4. 检查是否存在Debug构建输出
    elseif(EXISTS "${CMAKE_BINARY_DIR}/MQTTGateway/Debug/MQTTGateway.exe")
        set(ACTUAL_BUILD_TYPE "Debug")
        message(STATUS "vcpkg DLL: 检测到Debug构建输出，使用Debug配置")
    # 5. 默认使用Debug（因为你主要使用Debug版本）
    else()
        set(ACTUAL_BUILD_TYPE "Debug")
        message(STATUS "vcpkg DLL: 无法检测构建类型，默认使用Debug配置")
    endif()

    # 根据构建类型确定DLL路径
    if(ACTUAL_BUILD_TYPE STREQUAL "Debug")
        set(VCPKG_BIN_DIR "${VCPKG_INSTALLED_DIR}/x64-windows/debug/bin")
        set(VCPKG_RELEASE_BIN_DIR "${VCPKG_INSTALLED_DIR}/x64-windows/bin")
        message(STATUS "Debug模式：使用 ${VCPKG_BIN_DIR}")
    else()
        set(VCPKG_BIN_DIR "${VCPKG_INSTALLED_DIR}/x64-windows/bin")
        set(VCPKG_RELEASE_BIN_DIR "${VCPKG_INSTALLED_DIR}/x64-windows/bin")
        message(STATUS "Release模式：使用 ${VCPKG_BIN_DIR}")
    endif()

    # 定义需要安装的DLL列表（修正了MQTT库的命名）
    set(VCPKG_DLLS
        # MQTT库 - 注意：Debug和Release版本名称相同
        "paho-mqtt3as.dll"
        "paho-mqtt3a.dll"
        "paho-mqttpp3.dll"

        # OpenSSL库 - 名称在Debug和Release中相同
        "libcrypto-3-x64.dll"
        "libssl-3-x64.dll"
    )
    
    # 安装DLL文件 - 改进的查找逻辑
    foreach(DLL_NAME ${VCPKG_DLLS})
        set(DLL_PATH "${VCPKG_BIN_DIR}/${DLL_NAME}")
        set(FALLBACK_DLL_PATH "${VCPKG_RELEASE_BIN_DIR}/${DLL_NAME}")
        set(DLL_FOUND FALSE)

        # 优先使用对应构建类型的DLL
        if(EXISTS "${DLL_PATH}")
            install(FILES "${DLL_PATH}"
                DESTINATION "${FSTATION_INSTALL_BINDIR}"
                COMPONENT Runtime
            )
            message(STATUS "✓ 将安装 vcpkg DLL (${ACTUAL_BUILD_TYPE}): ${DLL_PATH}")
            set(DLL_FOUND TRUE)
        elseif(NOT "${DLL_PATH}" STREQUAL "${FALLBACK_DLL_PATH}" AND EXISTS "${FALLBACK_DLL_PATH}")
            # 如果Debug路径不存在，尝试Release路径
            install(FILES "${FALLBACK_DLL_PATH}"
                DESTINATION "${FSTATION_INSTALL_BINDIR}"
                COMPONENT Runtime
            )
            message(STATUS "✓ 将安装 vcpkg DLL (fallback): ${FALLBACK_DLL_PATH}")
            set(DLL_FOUND TRUE)
        endif()

        # 如果还是找不到，记录警告
        if(NOT DLL_FOUND)
            message(WARNING "✗ 找不到 vcpkg DLL: ${DLL_NAME}")
            message(STATUS "  查找路径: ${DLL_PATH}")
            if(NOT "${DLL_PATH}" STREQUAL "${FALLBACK_DLL_PATH}")
                message(STATUS "  备用路径: ${FALLBACK_DLL_PATH}")
            endif()
        endif()
    endforeach()
    
    # 安装构建输出目录中的DLL（作为最后的备用方案）
    set(BUILD_OUTPUT_DIRS
        "${CMAKE_BINARY_DIR}/MQTTGateway/${ACTUAL_BUILD_TYPE}"
        "${CMAKE_BINARY_DIR}/MQTTGateway/Debug"
        "${CMAKE_BINARY_DIR}/MQTTGateway/Release"
    )

    foreach(BUILD_DIR ${BUILD_OUTPUT_DIRS})
        if(EXISTS "${BUILD_DIR}")
            file(GLOB BUILD_DLLS "${BUILD_DIR}/*.dll")
            foreach(DLL_FILE ${BUILD_DLLS})
                get_filename_component(DLL_NAME ${DLL_FILE} NAME)
                # 只安装vcpkg相关的DLL
                if(DLL_NAME MATCHES "paho-|libcrypto|libssl|zlib")
                    install(FILES "${DLL_FILE}"
                        DESTINATION "${FSTATION_INSTALL_BINDIR}"
                        COMPONENT Runtime
                    )
                    message(STATUS "✓ 将安装构建输出 DLL: ${DLL_FILE}")
                endif()
            endforeach()
            break()  # 找到第一个存在的目录就停止
        endif()
    endforeach()

    message(STATUS "vcpkg DLL安装配置完成")
endfunction()

# 安装单个VC++运行时库DLL的辅助函数
function(install_single_vcrt_dll DLL_NAME VS_REDIST_DIRS SYSTEM_DIRS)
    set(DLL_FOUND FALSE)

    # 首先尝试从Visual Studio Redist目录
    foreach(VS_DIR ${VS_REDIST_DIRS})
        if(EXISTS "${VS_DIR}")
            file(GLOB_RECURSE REDIST_DLL "${VS_DIR}/*/${DLL_NAME}")
            if(REDIST_DLL)
                list(GET REDIST_DLL 0 FIRST_DLL)
                install(FILES "${FIRST_DLL}"
                    DESTINATION "${FSTATION_INSTALL_BINDIR}"
                    COMPONENT Runtime
                    OPTIONAL
                )
                message(STATUS "✓ 将安装 VC++ Runtime DLL: ${FIRST_DLL}")
                set(DLL_FOUND TRUE)
                break()
            endif()
        endif()
    endforeach()

    # 如果没找到，尝试从系统目录
    if(NOT DLL_FOUND)
        foreach(SYS_DIR ${SYSTEM_DIRS})
            if(EXISTS "${SYS_DIR}/${DLL_NAME}")
                install(FILES "${SYS_DIR}/${DLL_NAME}"
                    DESTINATION "${FSTATION_INSTALL_BINDIR}"
                    COMPONENT Runtime
                    OPTIONAL
                )
                message(STATUS "✓ 将安装系统 VC++ Runtime DLL: ${SYS_DIR}/${DLL_NAME}")
                set(DLL_FOUND TRUE)
                break()
            endif()
        endforeach()
    endif()

    if(NOT DLL_FOUND)
        message(STATUS "⚠ 找不到 VC++ Runtime DLL: ${DLL_NAME}")
    endif()
endfunction()

# 安装Qt库的函数 - 使用Qt官方部署工具
function(install_qt_dlls)
    if(NOT WIN32)
        return()
    endif()

    # 首先尝试查找Qt6
    find_package(Qt6 QUIET COMPONENTS Core Network)

    if(NOT Qt6_FOUND)
        message(WARNING "Qt6未找到，跳过Qt DLL安装")
        return()
    endif()

    # 查找Qt安装路径
    get_target_property(QT_CORE_LOCATION Qt6::Core IMPORTED_LOCATION_RELEASE)
    if(NOT QT_CORE_LOCATION)
        get_target_property(QT_CORE_LOCATION Qt6::Core IMPORTED_LOCATION_DEBUG)
    endif()

    if(NOT QT_CORE_LOCATION)
        message(WARNING "无法找到Qt6::Core库位置，跳过Qt DLL安装")
        return()
    endif()

    # 获取Qt bin目录
    get_filename_component(QT_BIN_DIR ${QT_CORE_LOCATION} DIRECTORY)
    get_filename_component(QT_INSTALL_PREFIX ${QT_BIN_DIR} DIRECTORY)

    message(STATUS "Qt安装路径: ${QT_INSTALL_PREFIX}")
    message(STATUS "Qt bin目录: ${QT_BIN_DIR}")
    message(STATUS "Qt Core库位置: ${QT_CORE_LOCATION}")

    # 查找windeployqt工具
    find_program(WINDEPLOYQT_EXECUTABLE
        NAMES windeployqt.exe windeployqt
        PATHS ${QT_BIN_DIR}
        NO_DEFAULT_PATH
    )

    # 无论是否找到windeployqt，都手动安装基本的Qt DLL作为备用
    message(STATUS "手动安装Qt DLL作为备用方案")
    set(QT_DLLS
        "Qt6Core.dll"
        "Qt6Network.dll"
    )

    foreach(DLL_NAME ${QT_DLLS})
        set(QT_DLL_PATH "${QT_BIN_DIR}/${DLL_NAME}")
        if(EXISTS "${QT_DLL_PATH}")
            install(FILES "${QT_DLL_PATH}"
                DESTINATION "${FSTATION_INSTALL_BINDIR}"
                COMPONENT Runtime
            )
            message(STATUS "✓ 将安装Qt DLL: ${QT_DLL_PATH}")
        else()
            message(WARNING "✗ 找不到Qt DLL: ${QT_DLL_PATH}")
        endif()
    endforeach()

    # 安装platforms插件
    set(QT_PLATFORMS_DIR "${QT_INSTALL_PREFIX}/plugins/platforms")
    if(EXISTS "${QT_PLATFORMS_DIR}")
        install(DIRECTORY "${QT_PLATFORMS_DIR}/"
            DESTINATION "${FSTATION_INSTALL_BINDIR}/platforms"
            COMPONENT Runtime
            FILES_MATCHING PATTERN "*.dll"
        )
        message(STATUS "✓ 将安装Qt platforms插件")
    endif()

    if(NOT WINDEPLOYQT_EXECUTABLE)
        message(WARNING "找不到windeployqt工具，仅使用手动安装的Qt DLL")
    else()
        message(STATUS "找到windeployqt: ${WINDEPLOYQT_EXECUTABLE}")

        # 创建一个安装时执行的脚本
        set(WINDEPLOYQT_SCRIPT "${CMAKE_CURRENT_BINARY_DIR}/windeployqt_install.cmake")
        file(WRITE "${WINDEPLOYQT_SCRIPT}" "
# Qt部署脚本 - 在安装时执行
message(STATUS \"正在使用windeployqt部署Qt依赖...\")

# 检测构建类型和目标文件
set(TARGET_EXE \"\${CMAKE_INSTALL_PREFIX}/${FSTATION_INSTALL_BINDIR}/MQTTGateway.exe\")
set(WORKING_DIR \"\${CMAKE_INSTALL_PREFIX}/${FSTATION_INSTALL_BINDIR}\")

# 转换为Windows路径格式
file(TO_NATIVE_PATH \"\${TARGET_EXE}\" TARGET_EXE_NATIVE)
file(TO_NATIVE_PATH \"\${WORKING_DIR}\" WORKING_DIR_NATIVE)

message(STATUS \"检查目标文件: \${TARGET_EXE}\")
if(NOT EXISTS \"\${TARGET_EXE}\")
    message(FATAL_ERROR \"找不到目标可执行文件: \${TARGET_EXE}\")
endif()

# 执行windeployqt
execute_process(
    COMMAND \"${WINDEPLOYQT_EXECUTABLE}\"
        --no-translations
        --no-system-d3d-compiler
        --no-opengl-sw
        --no-compiler-runtime
        \"\${TARGET_EXE_NATIVE}\"
    WORKING_DIRECTORY \"\${WORKING_DIR}\"
    RESULT_VARIABLE WINDEPLOYQT_RESULT
    OUTPUT_VARIABLE WINDEPLOYQT_OUTPUT
    ERROR_VARIABLE WINDEPLOYQT_ERROR
)

if(WINDEPLOYQT_RESULT EQUAL 0)
    message(STATUS \"✓ windeployqt执行成功\")
    message(STATUS \"输出: \${WINDEPLOYQT_OUTPUT}\")
else()
    message(WARNING \"⚠ windeployqt执行失败 (返回码: \${WINDEPLOYQT_RESULT})\")
    message(STATUS \"错误: \${WINDEPLOYQT_ERROR}\")
endif()
")

        # 安装时执行Qt部署脚本
        install(SCRIPT "${WINDEPLOYQT_SCRIPT}" COMPONENT Runtime)
        message(STATUS "✓ 已配置windeployqt自动部署")
    endif()

    message(STATUS "Qt库安装配置完成")
endfunction()

# 安装Visual C++运行时库DLL的函数
function(install_vcrt_dlls)
    if(NOT WIN32)
        return()
    endif()

    # 为Debug和Release版本都安装对应的运行时库
    # 这样可以确保无论使用哪个配置都能正常运行

    # Debug版本的运行时库
    set(DEBUG_VCRT_DLLS
        "vcruntime140d.dll"
        "vcruntime140_1d.dll"
        "msvcp140d.dll"
        "concrt140d.dll"
        "ucrtbased.dll"
    )

    # Release版本的运行时库
    set(RELEASE_VCRT_DLLS
        "vcruntime140.dll"
        "vcruntime140_1.dll"
        "msvcp140.dll"
        "concrt140.dll"
        "ucrtbase.dll"
    )

    # 查找Visual Studio安装目录
    set(VS_REDIST_DIRS
        "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Redist/MSVC"
        "C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Redist/MSVC"
        "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/VC/Redist/MSVC"
        "C:/Program Files (x86)/Microsoft Visual Studio/2019/Community/VC/Redist/MSVC"
        "C:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/VC/Redist/MSVC"
        "C:/Program Files (x86)/Microsoft Visual Studio/2019/Enterprise/VC/Redist/MSVC"
    )

    # 查找系统目录中的运行时库
    set(SYSTEM_DIRS
        "C:/Windows/System32"
        "C:/Windows/SysWOW64"
    )

    # 安装Debug版本的运行时库
    message(STATUS "安装Debug版本的VC++运行时库...")
    foreach(DLL_NAME ${DEBUG_VCRT_DLLS})
        install_single_vcrt_dll("${DLL_NAME}" "${VS_REDIST_DIRS}" "${SYSTEM_DIRS}")
    endforeach()

    # 安装Release版本的运行时库
    message(STATUS "安装Release版本的VC++运行时库...")
    foreach(DLL_NAME ${RELEASE_VCRT_DLLS})
        install_single_vcrt_dll("${DLL_NAME}" "${VS_REDIST_DIRS}" "${SYSTEM_DIRS}")
    endforeach()

    # 安装额外的系统DLL
    message(STATUS "安装额外的系统DLL...")
    # 添加一些额外的系统DLL
    set(EXTRA_SYSTEM_DLLS
        "api-ms-win-crt-runtime-l1-1-0.dll"
        "api-ms-win-crt-heap-l1-1-0.dll"
        "api-ms-win-crt-stdio-l1-1-0.dll"
        "api-ms-win-crt-string-l1-1-0.dll"
        "api-ms-win-crt-convert-l1-1-0.dll"
        "api-ms-win-crt-environment-l1-1-0.dll"
        "api-ms-win-crt-filesystem-l1-1-0.dll"
        "api-ms-win-crt-locale-l1-1-0.dll"
        "api-ms-win-crt-math-l1-1-0.dll"
        "api-ms-win-crt-multibyte-l1-1-0.dll"
        "api-ms-win-crt-process-l1-1-0.dll"
        "api-ms-win-crt-time-l1-1-0.dll"
        "api-ms-win-crt-utility-l1-1-0.dll"
    )

    foreach(DLL_NAME ${EXTRA_SYSTEM_DLLS})
        install_single_vcrt_dll("${DLL_NAME}" "" "${SYSTEM_DIRS}")
    endforeach()
endfunction()

# 安装共享头文件的函数
function(install_shared_headers)
    # 检查并安装协议头文件（如果存在）
    set(PROTOCOL_HEADERS
        "${CMAKE_SOURCE_DIR}/Common/Protocol/SocketProtocol.h"
        "${CMAKE_SOURCE_DIR}/../FStationMFC/FStation/SocketInterface.h"
        "${CMAKE_SOURCE_DIR}/../FStationMFC/FStation/MQTTInterface.h"
    )

    foreach(HEADER_FILE ${PROTOCOL_HEADERS})
        if(EXISTS "${HEADER_FILE}")
            get_filename_component(HEADER_NAME "${HEADER_FILE}" NAME)
            install(FILES "${HEADER_FILE}"
                DESTINATION "${FSTATION_INSTALL_INCLUDEDIR}/fstation/protocol"
                COMPONENT Development
                OPTIONAL
            )
            message(STATUS "将安装协议头文件: ${HEADER_FILE}")
        else()
            message(STATUS "跳过不存在的协议头文件: ${HEADER_FILE}")
        endif()
    endforeach()
endfunction()

# 安装配置文件的函数
function(install_config_files)
    # 检查配置文件是否存在并安装
    set(CONFIG_FILES
        "gateway.json"
        "data_points.json"
        "events.json"
        "commands.json"
        "subscriptions.json"
        "publish_topics.json"
    )

    set(CONFIG_SOURCE_DIR "${CMAKE_SOURCE_DIR}/MQTTGateway/config")

    # 1. 安装到标准位置 (share/fstation/config)
    foreach(CONFIG_FILE ${CONFIG_FILES})
        set(CONFIG_PATH "${CONFIG_SOURCE_DIR}/${CONFIG_FILE}")
        if(EXISTS "${CONFIG_PATH}")
            install(FILES "${CONFIG_PATH}"
                DESTINATION "${FSTATION_INSTALL_CONFIGDIR}"
                COMPONENT Runtime
            )
            message(STATUS "✓ 将安装配置文件到标准位置: ${CONFIG_FILE}")
        else()
            message(WARNING "✗ 配置文件不存在: ${CONFIG_PATH}")
        endif()
    endforeach()

    # 2. 同时安装到程序期望的位置 (bin/config) 以保持兼容性
    foreach(CONFIG_FILE ${CONFIG_FILES})
        set(CONFIG_PATH "${CONFIG_SOURCE_DIR}/${CONFIG_FILE}")
        if(EXISTS "${CONFIG_PATH}")
            install(FILES "${CONFIG_PATH}"
                DESTINATION "${FSTATION_INSTALL_BINDIR}/config"
                COMPONENT Runtime
            )
            message(STATUS "✓ 将安装配置文件到程序目录: ${CONFIG_FILE}")
        endif()
    endforeach()

    # 3. 创建特殊的scada_ctrl_conf.json文件（程序期望的主配置文件）
    # 直接复制gateway.json作为scada_ctrl_conf.json
    set(GATEWAY_CONFIG_PATH "${CONFIG_SOURCE_DIR}/gateway.json")
    if(EXISTS "${GATEWAY_CONFIG_PATH}")
        install(FILES "${GATEWAY_CONFIG_PATH}"
            DESTINATION "${FSTATION_INSTALL_BINDIR}/config"
            RENAME "scada_ctrl_conf.json"
            COMPONENT Runtime
        )
        message(STATUS "✓ 将复制gateway.json作为scada_ctrl_conf.json")
    endif()

    message(STATUS "配置文件安装配置完成")
endfunction()

# 安装文档的函数
function(install_documentation)
    # 安装主要文档
    install(FILES
        "${CMAKE_SOURCE_DIR}/doc/快速操作手册.md"
        "${CMAKE_SOURCE_DIR}/doc/接口集成指南.md"
        "${CMAKE_SOURCE_DIR}/doc/部署和测试指南.md"
        DESTINATION "${FSTATION_INSTALL_DOCDIR}"
        COMPONENT Documentation
        OPTIONAL
    )
    
    # 安装MQTT Gateway文档
    install(DIRECTORY
        "${CMAKE_SOURCE_DIR}/doc_mqtt_gateway/"
        DESTINATION "${FSTATION_INSTALL_DOCDIR}/mqtt_gateway"
        COMPONENT Documentation
        OPTIONAL
        PATTERN "*.md"
    )
    
    # 安装重要文档
    install(FILES
        "${CMAKE_SOURCE_DIR}/doc/重要/SCADA平台MQTT协议设备接入指南.md"
        "${CMAKE_SOURCE_DIR}/doc/重要/科瑞F站--数采清单.md"
        DESTINATION "${FSTATION_INSTALL_DOCDIR}/重要"
        COMPONENT Documentation
        OPTIONAL
    )
endfunction()

# 创建包配置文件的函数
function(create_package_config)
    # 设置包配置文件的安装路径
    set(FSTATION_CMAKE_CONFIG_DIR "${CMAKE_INSTALL_LIBDIR}/cmake/FStation")
    
    # 配置包配置文件
    configure_file(
        "${CMAKE_SOURCE_DIR}/cmake/FStationConfig.cmake.in"
        "${CMAKE_CURRENT_BINARY_DIR}/FStationConfig.cmake"
        @ONLY
    )
    
    # 创建版本配置文件
    write_basic_package_version_file(
        "${CMAKE_CURRENT_BINARY_DIR}/FStationConfigVersion.cmake"
        VERSION ${PROJECT_VERSION}
        COMPATIBILITY SameMajorVersion
    )
    
    # 安装包配置文件
    install(FILES
        "${CMAKE_CURRENT_BINARY_DIR}/FStationConfig.cmake"
        "${CMAKE_CURRENT_BINARY_DIR}/FStationConfigVersion.cmake"
        DESTINATION "${FSTATION_CMAKE_CONFIG_DIR}"
        COMPONENT Development
    )
endfunction()

# 配置CPack的函数
function(configure_cpack)
    # 基本信息
    set(CPACK_PACKAGE_NAME "FStation")
    set(CPACK_PACKAGE_VENDOR "科瑞")
    set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "FStation MQTT Gateway and UI Application")
    set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
    set(CPACK_PACKAGE_VERSION_MAJOR ${PROJECT_VERSION_MAJOR})
    set(CPACK_PACKAGE_VERSION_MINOR ${PROJECT_VERSION_MINOR})
    set(CPACK_PACKAGE_VERSION_PATCH ${PROJECT_VERSION_PATCH})
    
    # 安装目录
    set(CPACK_PACKAGE_INSTALL_DIRECTORY "FStation")
    
    # 组件配置
    set(CPACK_COMPONENTS_ALL Runtime Development Documentation)
    
    # 组件描述
    set(CPACK_COMPONENT_RUNTIME_DISPLAY_NAME "运行时文件")
    set(CPACK_COMPONENT_RUNTIME_DESCRIPTION "可执行文件、DLL和配置文件")
    set(CPACK_COMPONENT_RUNTIME_REQUIRED TRUE)
    
    set(CPACK_COMPONENT_DEVELOPMENT_DISPLAY_NAME "开发文件")
    set(CPACK_COMPONENT_DEVELOPMENT_DESCRIPTION "头文件、静态库和CMake配置文件")
    set(CPACK_COMPONENT_DEVELOPMENT_DEPENDS Runtime)
    
    set(CPACK_COMPONENT_DOCUMENTATION_DISPLAY_NAME "文档")
    set(CPACK_COMPONENT_DOCUMENTATION_DESCRIPTION "用户手册和开发文档")
    
    # Windows特定配置
    if(WIN32)
        set(CPACK_GENERATOR "WIX;ZIP")
        set(CPACK_WIX_UPGRADE_GUID "12345678-1234-1234-1234-123456789012")
        set(CPACK_WIX_PRODUCT_GUID "*************-4321-4321-************")
        set(CPACK_WIX_PRODUCT_ICON "${CMAKE_SOURCE_DIR}/FStationMFC/FStation/res/FStation.ico")
        
        # 创建开始菜单快捷方式
        set(CPACK_PACKAGE_EXECUTABLES "MQTTGatewayUI;FStation MQTT Gateway UI")
        set(CPACK_CREATE_DESKTOP_LINKS "MQTTGatewayUI")
    endif()
    
    # 设置到父作用域
    set(CPACK_PACKAGE_NAME "${CPACK_PACKAGE_NAME}" PARENT_SCOPE)
    set(CPACK_PACKAGE_VENDOR "${CPACK_PACKAGE_VENDOR}" PARENT_SCOPE)
    set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "${CPACK_PACKAGE_DESCRIPTION_SUMMARY}" PARENT_SCOPE)
    set(CPACK_PACKAGE_VERSION "${CPACK_PACKAGE_VERSION}" PARENT_SCOPE)
    set(CPACK_PACKAGE_VERSION_MAJOR "${CPACK_PACKAGE_VERSION_MAJOR}" PARENT_SCOPE)
    set(CPACK_PACKAGE_VERSION_MINOR "${CPACK_PACKAGE_VERSION_MINOR}" PARENT_SCOPE)
    set(CPACK_PACKAGE_VERSION_PATCH "${CPACK_PACKAGE_VERSION_PATCH}" PARENT_SCOPE)
    set(CPACK_PACKAGE_INSTALL_DIRECTORY "${CPACK_PACKAGE_INSTALL_DIRECTORY}" PARENT_SCOPE)
    set(CPACK_COMPONENTS_ALL "${CPACK_COMPONENTS_ALL}" PARENT_SCOPE)
    set(CPACK_COMPONENT_RUNTIME_DISPLAY_NAME "${CPACK_COMPONENT_RUNTIME_DISPLAY_NAME}" PARENT_SCOPE)
    set(CPACK_COMPONENT_RUNTIME_DESCRIPTION "${CPACK_COMPONENT_RUNTIME_DESCRIPTION}" PARENT_SCOPE)
    set(CPACK_COMPONENT_RUNTIME_REQUIRED "${CPACK_COMPONENT_RUNTIME_REQUIRED}" PARENT_SCOPE)
    set(CPACK_COMPONENT_DEVELOPMENT_DISPLAY_NAME "${CPACK_COMPONENT_DEVELOPMENT_DISPLAY_NAME}" PARENT_SCOPE)
    set(CPACK_COMPONENT_DEVELOPMENT_DESCRIPTION "${CPACK_COMPONENT_DEVELOPMENT_DESCRIPTION}" PARENT_SCOPE)
    set(CPACK_COMPONENT_DEVELOPMENT_DEPENDS "${CPACK_COMPONENT_DEVELOPMENT_DEPENDS}" PARENT_SCOPE)
    set(CPACK_COMPONENT_DOCUMENTATION_DISPLAY_NAME "${CPACK_COMPONENT_DOCUMENTATION_DISPLAY_NAME}" PARENT_SCOPE)
    set(CPACK_COMPONENT_DOCUMENTATION_DESCRIPTION "${CPACK_COMPONENT_DOCUMENTATION_DESCRIPTION}" PARENT_SCOPE)
    
    if(WIN32)
        set(CPACK_GENERATOR "${CPACK_GENERATOR}" PARENT_SCOPE)
        set(CPACK_WIX_UPGRADE_GUID "${CPACK_WIX_UPGRADE_GUID}" PARENT_SCOPE)
        set(CPACK_WIX_PRODUCT_GUID "${CPACK_WIX_PRODUCT_GUID}" PARENT_SCOPE)
        set(CPACK_WIX_PRODUCT_ICON "${CPACK_WIX_PRODUCT_ICON}" PARENT_SCOPE)
        set(CPACK_PACKAGE_EXECUTABLES "${CPACK_PACKAGE_EXECUTABLES}" PARENT_SCOPE)
        set(CPACK_CREATE_DESKTOP_LINKS "${CPACK_CREATE_DESKTOP_LINKS}" PARENT_SCOPE)
    endif()
endfunction() 