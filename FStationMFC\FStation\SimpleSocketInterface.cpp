﻿#include "StdAfx.h"
#include "SimpleSocketInterface.h"
#include "Global/Dat.h"
#include "json/json.h"
#include "Global/Global.h"
#include "ScadaCommandHandler.h"

// 使用标准命名空间
using namespace std;

#pragma comment(lib, "ws2_32.lib")

// 静态成员初始化
SimpleSocketInterface* SimpleSocketInterface::m_instance = NULL;
CRITICAL_SECTION SimpleSocketInterface::m_instanceMutex;
bool SimpleSocketInterface::m_instanceMutexInitialized = false;

SimpleSocketInterface::SimpleSocketInterface()
    : m_socket(-1), m_serverAddress("127.0.0.1"), m_serverPort(8888),
      m_connected(false), m_receiveThread(NULL), m_receiveRunning(false) {
    // 初始化Winsock
    WSADATA wsaData;
    WSAStartup(MAKEWORD(2, 2), &wsaData);

    // 初始化临界区
    InitializeCriticalSection(&m_responseMutex);
}

SimpleSocketInterface::~SimpleSocketInterface() {
    Disconnect();
    DeleteCriticalSection(&m_responseMutex);
    WSACleanup();
}

// 单例模式实现
SimpleSocketInterface* SimpleSocketInterface::GetInstance() {
    // 初始化临界区（只初始化一次）
    if (!m_instanceMutexInitialized) {
        InitializeCriticalSection(&m_instanceMutex);
        m_instanceMutexInitialized = true;
    }

    EnterCriticalSection(&m_instanceMutex);
    if (m_instance == NULL) {
        m_instance = new SimpleSocketInterface();
    }
    LeaveCriticalSection(&m_instanceMutex);
    return m_instance;
}

void SimpleSocketInterface::DestroyInstance() {
    if (!m_instanceMutexInitialized) {
        return;
    }

    EnterCriticalSection(&m_instanceMutex);
    if (m_instance != NULL) {
        delete m_instance;
        m_instance = NULL;
    }
    LeaveCriticalSection(&m_instanceMutex);

    DeleteCriticalSection(&m_instanceMutex);
    m_instanceMutexInitialized = false;
}

// 连接管理
bool SimpleSocketInterface::Connect(const std::string& serverAddress, int serverPort) {
    if (m_connected) {
        return true;
    }
    
    m_serverAddress = serverAddress;
    m_serverPort = serverPort;
    
    // 创建Socket
    SOCKET sock = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (sock == INVALID_SOCKET) {
        LogError(_T("创建Socket失败"));
        return false;
    }
    m_socket = static_cast<int>(sock);
    
    // 设置服务器地址
    sockaddr_in serverAddr;
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_port = htons(serverPort);
    inet_pton(AF_INET, serverAddress.c_str(), &serverAddr.sin_addr);
    
    // 连接到服务器
    if (connect(sock, (sockaddr*)&serverAddr, sizeof(serverAddr)) == SOCKET_ERROR) {
        LogError(_T("连接到MQTT网关失败"));
        closesocket(sock);
        m_socket = -1;
        return false;
    }
    
    m_connected = true;
    
    // 启动接收线程
    m_receiveRunning = true;
    m_receiveThread = (HANDLE)_beginthreadex(NULL, 0, ReceiveThreadFunc, this, 0, NULL);
    
    LogInfo(_T("已连接到MQTT网关"));
    return true;
}

void SimpleSocketInterface::Disconnect() {
    if (!m_connected) {
        return;
    }
    
    m_connected = false;
    m_receiveRunning = false;
    
    // 关闭Socket
    if (m_socket != -1) {
        closesocket(static_cast<SOCKET>(m_socket));
        m_socket = -1;
    }
    
    // 等待接收线程结束
    if (m_receiveThread != NULL) {
        WaitForSingleObject(m_receiveThread, 5000);
        CloseHandle(m_receiveThread);
        m_receiveThread = NULL;
    }
    
    LogInfo(_T("已断开MQTT网关连接"));
}

bool SimpleSocketInterface::IsConnected() const {
    return m_connected;
}

// 静态事件上报方法
bool SimpleSocketInterface::SendSnInEvent(const CString& sn, const CString& productionModel, const CString& profilesJson) {
    // 检查MQTT功能是否启用
    if (!VAR_MACHINE_B("MQTT功能启用")) {
        return true; // 返回true避免调用方认为是错误
    }

    SimpleSocketInterface* instance = GetInstance();
    if (!instance->IsConnected()) {
        if (!instance->Connect()) {
            return false;
        }
    }

    // 🔥 关键修改：事件发送前先发送最新设备状态，确保MQTT网关缓存是最新的
    if (!instance->SendDeviceStatus()) {
        instance->LogError(_T("发送设备状态失败，但继续发送事件"));
    }

    // 使用JsonCpp构建properties对象
    Json::Value properties;
    properties["sn"] = Json::Value(instance->SafeCStringToUTF8(sn));
    properties["production_model"] = Json::Value(instance->SafeCStringToUTF8(productionModel));
    properties["profiles"] = Json::Value(Json::arrayValue);

    Json::FastWriter writer;
    std::string propertiesStr = writer.write(properties);
    // 移除FastWriter添加的换行符
    if (!propertiesStr.empty() && propertiesStr.back() == '\n') {
        propertiesStr.pop_back();
    }

    return instance->SendEventWithResponse("EVENT_SN_IN", propertiesStr, 2000); // 2秒超时
}

bool SimpleSocketInterface::SendSnOutEvent(const CString& sn, const CString& productionModel, const CString& profilesJson) {
    // 检查MQTT功能是否启用
    if (!VAR_MACHINE_B("MQTT功能启用")) {
        return true; // 返回true避免调用方认为是错误
    }

    SimpleSocketInterface* instance = GetInstance();
    if (!instance->IsConnected()) {
        if (!instance->Connect()) {
            return false;
        }
    }

    // 🔥 关键修改：事件发送前先发送最新设备状态，确保MQTT网关缓存是最新的
    if (!instance->SendDeviceStatus()) {
        instance->LogError(_T("发送设备状态失败，但继续发送事件"));
    }

    // 使用JsonCpp构建properties对象
    Json::Value properties;
    properties["sn"] = Json::Value(instance->SafeCStringToUTF8(sn));
    properties["production_model"] = Json::Value(instance->SafeCStringToUTF8(productionModel));
    properties["profiles"] = Json::Value(Json::arrayValue);

    Json::FastWriter writer;
    std::string propertiesStr = writer.write(properties);
    // 移除FastWriter添加的换行符
    if (!propertiesStr.empty() && propertiesStr.back() == '\n') {
        propertiesStr.pop_back();
    }

    return instance->SendEventWithResponse("EVENT_SN_OUT", propertiesStr, 2000); // 2秒超时
}

bool SimpleSocketInterface::SendSnOutReqEvent(const CString& sn, const CString& productionModel, const CString& profilesJson) {
    // 检查MQTT功能是否启用
    if (!VAR_MACHINE_B("MQTT功能启用")) {
        return true; // 返回true避免调用方认为是错误
    }

    SimpleSocketInterface* instance = GetInstance();
    if (!instance->IsConnected()) {
        if (!instance->Connect()) {
            return false;
        }
    }

    // 🔥 关键修改：事件发送前先发送最新设备状态，确保MQTT网关缓存是最新的
    if (!instance->SendDeviceStatus()) {
        instance->LogError(_T("发送设备状态失败，但继续发送事件"));
    }

    // 使用JsonCpp构建properties对象
    Json::Value properties;
    properties["sn"] = Json::Value(instance->SafeCStringToUTF8(sn));
    properties["production_model"] = Json::Value(instance->SafeCStringToUTF8(productionModel));
    properties["profiles"] = Json::Value(Json::arrayValue);

    Json::FastWriter writer;
    std::string propertiesStr = writer.write(properties);
    // 移除FastWriter添加的换行符
    if (!propertiesStr.empty() && propertiesStr.back() == '\n') {
        propertiesStr.pop_back();
    }

    return instance->SendEventWithResponse("EVENT_SN_OUT_REQ", propertiesStr, 2000); // 2秒超时
}

bool SimpleSocketInterface::SendBopDeliverCompletedEvent(const CString& requestId) {
    // 检查MQTT功能是否启用
    if (!VAR_MACHINE_B("MQTT功能启用")) {
        return true; // 返回true避免调用方认为是错误
    }

    SimpleSocketInterface* instance = GetInstance();
    if (!instance->IsConnected()) {
        if (!instance->Connect()) {
            return false;
        }
    }

    // 🔥 关键修改：事件发送前先发送最新设备状态，确保MQTT网关缓存是最新的
    if (!instance->SendDeviceStatus()) {
        instance->LogError(_T("发送设备状态失败，但继续发送事件"));
    }

    // 使用JsonCpp构建properties对象
    Json::Value properties;
    if (!requestId.IsEmpty()) {
        properties["request_id"] = Json::Value(instance->SafeCStringToUTF8(requestId));
    }

    Json::FastWriter writer;
    std::string propertiesStr = writer.write(properties);
    // 移除FastWriter添加的换行符
    if (!propertiesStr.empty() && propertiesStr.back() == '\n') {
        propertiesStr.pop_back();
    }

    return instance->SendEventWithResponse("EVENT_BOP_DELIVER_COMPLETED", propertiesStr, 2000); // 2秒超时
}

bool SimpleSocketInterface::SendPauseEvent(const CString& sn, const CString& pauseMsg, const CString& productionModel, const CString& profilesJson) {
    // 检查MQTT功能是否启用
    if (!VAR_MACHINE_B("MQTT功能启用")) {
        return true; // 返回true避免调用方认为是错误
    }

    SimpleSocketInterface* instance = GetInstance();
    if (!instance->IsConnected()) {
        if (!instance->Connect()) {
            return false;
        }
    }

    // 🔥 关键修改：事件发送前先发送最新设备状态，确保MQTT网关缓存是最新的
    if (!instance->SendDeviceStatus()) {
        instance->LogError(_T("发送设备状态失败，但继续发送事件"));
    }

    // 使用JsonCpp构建properties对象
    Json::Value properties;
    properties["sn"] = Json::Value(instance->SafeCStringToUTF8(sn));
    properties["pause_msg"] = Json::Value(instance->SafeCStringToUTF8(pauseMsg));

    if (!productionModel.IsEmpty()) {
        properties["production_model"] = Json::Value(instance->SafeCStringToUTF8(productionModel));
    }

    properties["profiles"] = Json::Value(Json::arrayValue);

    Json::FastWriter writer;
    std::string propertiesStr = writer.write(properties);
    // 移除FastWriter添加的换行符
    if (!propertiesStr.empty() && propertiesStr.back() == '\n') {
        propertiesStr.pop_back();
    }

    return instance->SendEventWithResponse("EVENT_PAUSE", propertiesStr, 2000); // 2秒超时
}

bool SimpleSocketInterface::SendFaultEvent(const CString& sn, const CString& faultCode, const CString& faultMsg, const CString& faultType, const CString& productionModel, const CString& profilesJson) {
    // 检查MQTT功能是否启用
    if (!VAR_MACHINE_B("MQTT功能启用")) {
        return true; // 返回true避免调用方认为是错误
    }

    SimpleSocketInterface* instance = GetInstance();
    if (!instance->IsConnected()) {
        if (!instance->Connect()) {
            return false;
        }
    }

    // 🔥 关键修改：事件发送前先发送最新设备状态，确保MQTT网关缓存是最新的
    if (!instance->SendDeviceStatus()) {
        instance->LogError(_T("发送设备状态失败，但继续发送事件"));
    }

    // 使用JsonCpp构建properties对象
    Json::Value properties;
    properties["sn"] = Json::Value(instance->SafeCStringToUTF8(sn));
    properties["fault_code"] = Json::Value(instance->SafeCStringToUTF8(faultCode));
    properties["fault_msg"] = Json::Value(instance->SafeCStringToUTF8(faultMsg));
    properties["fault_type"] = Json::Value(instance->SafeCStringToUTF8(faultType));

    if (!productionModel.IsEmpty()) {
        properties["production_model"] = Json::Value(instance->SafeCStringToUTF8(productionModel));
    }

    properties["profiles"] = Json::Value(Json::arrayValue);

    Json::FastWriter writer;
    std::string propertiesStr = writer.write(properties);
    // 移除FastWriter添加的换行符
    if (!propertiesStr.empty() && propertiesStr.back() == '\n') {
        propertiesStr.pop_back();
    }

    return instance->SendEventWithResponse("EVENT_FAULT", propertiesStr, 2000); // 2秒超时
}

bool SimpleSocketInterface::SendDeviceStatus() {
    // 检查MQTT功能是否启用
    if (!VAR_MACHINE_B("MQTT功能启用")) {
        return true; // 返回true避免调用方认为是错误
    }

    SimpleSocketInterface* instance = GetInstance();
    if (!instance->IsConnected()) {
        if (!instance->Connect()) {
            return false;
        }
    }

    // 收集所有数据点
    std::string allDataPoints = instance->CollectAllDataPoints();

    // 发送设备状态（无需等待答复）
    return instance->SendMessage("DEVICE_STATUS", allDataPoints);
}

bool SimpleSocketInterface::SendCommandResponse(const std::string& requestId, const std::string& responseJson) {
    SimpleSocketInterface* instance = GetInstance();
    if (!instance || !instance->IsConnected()) {
        return false;
    }

    return instance->SendMessage("COMMAND_RESPONSE", responseJson, requestId);
}

// 内部方法实现
bool SimpleSocketInterface::SendMessage(const std::string& messageType, const std::string& jsonData, const std::string& requestId) {
    if (!m_connected || m_socket == -1) {
        return false;
    }

    SOCKET sock = static_cast<SOCKET>(m_socket);

    // 使用JsonCpp构建消息对象
    Json::Value message;
    message["type"] = Json::Value(messageType);
    message["timestamp"] = Json::Value(GetCurrentTimestamp());

    // 解析jsonData字符串为Json::Value对象
    Json::Reader reader;
    Json::Value dataValue;
    if (reader.parse(jsonData, dataValue)) {
        message["data"] = dataValue;
    } else {
        // 如果解析失败，作为字符串处理
        message["data"] = Json::Value(jsonData);
    }

    if (!requestId.empty()) {
        message["requestId"] = Json::Value(requestId);
    }

    Json::FastWriter writer;
    std::string messageStr = writer.write(message);
    // 移除FastWriter添加的换行符
    if (!messageStr.empty() && messageStr.back() == '\n') {
        messageStr.pop_back();
    }

    // 调试输出：显示要发送的消息
    LogInfo(CString(_T("准备发送消息: ")) + CString(messageStr.c_str()));

    // 添加消息长度前缀
    DWORD messageLength = static_cast<DWORD>(messageStr.length());
    std::string packet = std::string(reinterpret_cast<char*>(&messageLength), sizeof(messageLength)) + messageStr;

    // 发送消息
    int totalSent = 0;
    int packetSize = static_cast<int>(packet.length());

    while (totalSent < packetSize) {
        int sent = send(sock, packet.c_str() + totalSent, packetSize - totalSent, 0);
        if (sent == SOCKET_ERROR) {
            LogError(_T("发送消息失败"));
            return false;
        }
        totalSent += sent;
    }

    return true;
}

bool SimpleSocketInterface::SendEventWithResponse(const std::string& eventType, const std::string& properties, int timeoutMs) {
    // 生成request_id
    std::string requestId = GenerateRequestId();

    // 构建事件消息
    std::string eventMessage = BuildEventMessage(eventType, properties);

    // 调试输出：显示构建的事件消息
    LogInfo(CString(_T("构建事件消息: ")) + CString(eventMessage.c_str()));

    // 注册待处理响应
    EnterCriticalSection(&m_responseMutex);
    PendingResponse response;
    response.timestamp = GetTickCount();
    response.received = false;
    response.success = false;
    m_pendingResponses[requestId] = response;
    LeaveCriticalSection(&m_responseMutex);

    // 发送事件消息
    if (!SendMessage(eventType, eventMessage, requestId)) {
        // 清理待处理响应
        EnterCriticalSection(&m_responseMutex);
        m_pendingResponses.erase(requestId);
        LeaveCriticalSection(&m_responseMutex);
        return false;
    }

    LogInfo(CString(_T("事件已发送，等待答复: ")) + CString(eventType.c_str()) + _T(", RequestId: ") + CString(requestId.c_str()));

    // 等待响应
    return WaitForResponse(requestId, timeoutMs);
}

bool SimpleSocketInterface::WaitForResponse(const std::string& requestId, int timeoutMs) {
    DWORD startTime = GetTickCount();

    while (true) {
        // 检查是否收到响应
        EnterCriticalSection(&m_responseMutex);
        std::map<std::string, PendingResponse>::iterator it = m_pendingResponses.find(requestId);
        if (it != m_pendingResponses.end() && it->second.received) {
            bool success = it->second.success;
            std::string message = it->second.message;

            // 清理响应记录
            m_pendingResponses.erase(it);
            LeaveCriticalSection(&m_responseMutex);

            if (success) {
                LogInfo(CString(_T("事件答复成功: RequestId: ")) + CString(requestId.c_str()));
            } else {
                LogError(CString(_T("事件答复失败: RequestId: ")) + CString(requestId.c_str()) +
                        _T(", 错误: ") + CString(message.c_str()));
            }

            return success;
        }
        LeaveCriticalSection(&m_responseMutex);

        // 检查超时
        DWORD elapsed = GetTickCount() - startTime;
        if (elapsed >= (DWORD)timeoutMs) {
            LogError(CString(_T("事件答复超时: RequestId: ")) + CString(requestId.c_str()));

            // 清理超时的响应记录
            EnterCriticalSection(&m_responseMutex);
            m_pendingResponses.erase(requestId);
            LeaveCriticalSection(&m_responseMutex);
            return false;
        }

        // 短暂等待
        Sleep(50);
    }
}

// 接收线程
unsigned __stdcall SimpleSocketInterface::ReceiveThreadFunc(void* param) {
    SimpleSocketInterface* pThis = (SimpleSocketInterface*)param;
    char buffer[8192];

    while (pThis->m_receiveRunning && pThis->m_connected) {
        SOCKET sock = static_cast<SOCKET>(pThis->m_socket);

        // 首先接收消息长度
        DWORD messageLength = 0;
        int received = recv(sock, reinterpret_cast<char*>(&messageLength), sizeof(messageLength), 0);

        if (received <= 0) {
            if (pThis->m_receiveRunning) {
                pThis->LogError(_T("接收消息长度失败，连接可能已断开"));
                pThis->m_connected = false;
            }
            break;
        }

        // 接收消息内容
        std::string message;
        DWORD totalReceived = 0;

        while (totalReceived < messageLength && pThis->m_receiveRunning) {
            DWORD toReceive = messageLength - totalReceived;
            if (toReceive > sizeof(buffer)) {
                toReceive = sizeof(buffer);
            }
            received = recv(sock, buffer, static_cast<int>(toReceive), 0);

            if (received <= 0) {
                if (pThis->m_receiveRunning) {
                    pThis->LogError(_T("接收消息内容失败"));
                    pThis->m_connected = false;
                }
                return 0;
            }

            message.append(buffer, received);
            totalReceived += static_cast<DWORD>(received);
        }

        if (totalReceived == messageLength) {
            pThis->ProcessReceivedMessage(message);
        }
    }

    return 0;
}

// 日志方法
void SimpleSocketInterface::LogInfo(const CString& message) {
    // 简化版日志输出
    CString logMessage = CString(_T("[INFO] ")) + message;
    OutputDebugString(logMessage);
    OutputDebugString(_T("\n"));
}

void SimpleSocketInterface::LogError(const CString& message) {
    // 简化版错误日志输出
    CString logMessage = CString(_T("[ERROR] ")) + message;
    OutputDebugString(logMessage);
    OutputDebugString(_T("\n"));
}

void SimpleSocketInterface::ProcessReceivedMessage(const std::string& message) {
    try {
        // 处理MQTT网关转发的命令
        if (message.find("\"type\":\"COMMAND\"") != std::string::npos) {
            // 提取requestId
            std::string requestId;
            size_t pos = message.find("\"requestId\":\"");
            if (pos != std::string::npos) {
                pos += 13; // 跳过 "requestId":"
                size_t endPos = message.find("\"", pos);
                if (endPos != std::string::npos) {
                    requestId = message.substr(pos, endPos - pos);
                }
            }

            LogInfo(CString(_T("收到MQTT网关命令，RequestId: ")) + CString(requestId.c_str()));

            // 调用外部命令处理回调
            extern int OnSocketCommandReceived(const CString& command, const CString& params, const CString& requestId);

            // 使用JsonCpp解析完整的Socket消息
            Json::Reader reader;
            Json::Value socketRoot;
            if (reader.parse(message, socketRoot)) {
                if (socketRoot.isMember("data") && socketRoot["data"].isString()) {
                    std::string scadaCommandJson = socketRoot["data"].asString();

                    // 解析SCADA命令以提取command_name
                    Json::Value scadaRoot;
                    if (reader.parse(scadaCommandJson, scadaRoot)) {
                        std::string commandName = "UNKNOWN";
                        if (scadaRoot.isMember("command_name") && scadaRoot["command_name"].isString()) {
                            commandName = scadaRoot["command_name"].asString();
                        }

                        // 调用Socket命令处理回调函数
                        CString csCommand = CString(commandName.c_str());
                        CString csParams = _T("{}"); // 简化参数，实际参数在SCADA命令中
                        CString csRequestId = CString(requestId.c_str());

                        int result = OnSocketCommandReceived(csCommand, csParams, csRequestId);

                        if (result == 0) {
                            LogInfo(CString(_T("SCADA命令处理成功: ")) + csCommand);
                        } else {
                            LogError(CString(_T("SCADA命令处理失败: ")) + csCommand);
                        }
                    } else {
                        LogError(_T("SCADA命令JSON解析失败"));
                    }
                } else {
                    LogError(_T("Socket命令消息格式错误"));
                }
            } else {
                LogError(_T("Socket消息JSON解析失败"));
            }

            return; // 命令处理完成，直接返回
        }

        // 简化的JSON解析 - 查找关键字段
        if (message.find("EVENT_RESPONSE") != std::string::npos) {
            // 提取request_id
            std::string requestId;
            size_t pos = message.find("\"request_id\":\"");
            if (pos != std::string::npos) {
                pos += 14; // 跳过 "request_id":"
                size_t endPos = message.find("\"", pos);
                if (endPos != std::string::npos) {
                    requestId = message.substr(pos, endPos - pos);
                }
            }

            // 提取result_code
            int resultCode = -1;
            pos = message.find("\"result_code\":");
            if (pos != std::string::npos) {
                pos += 14; // 跳过 "result_code":
                resultCode = atoi(message.substr(pos, 10).c_str());
            }

            // 提取result_message
            std::string resultMessage;
            pos = message.find("\"result_message\":\"");
            if (pos != std::string::npos) {
                pos += 18; // 跳过 "result_message":"
                size_t endPos = message.find("\"", pos);
                if (endPos != std::string::npos) {
                    resultMessage = message.substr(pos, endPos - pos);
                }
            }

            // 更新响应状态
            if (!requestId.empty()) {
                EnterCriticalSection(&m_responseMutex);
                std::map<std::string, PendingResponse>::iterator it = m_pendingResponses.find(requestId);
                if (it != m_pendingResponses.end()) {
                    it->second.received = true;
                    it->second.success = (resultCode == 0);
                    it->second.message = resultMessage;
                }
                LeaveCriticalSection(&m_responseMutex);
            }
        }

    } catch (...) {
        LogError(_T("处理接收消息异常"));
    }
}

// 工具方法实现
std::string SimpleSocketInterface::GenerateRequestId() {
    // VS2010兼容的RequestId生成
    DWORD timestamp = GetTickCount();

    static int counter = 0;
    counter = (counter + 1) % 1000;

    char buffer[32];
    sprintf_s(buffer, sizeof(buffer), "%lu%03d", timestamp, counter);

    return std::string(buffer);
}

std::string SimpleSocketInterface::GetCurrentTimestamp() {
    // VS2010兼容的时间戳生成
    SYSTEMTIME st;
    GetLocalTime(&st);

    char buffer[32];
    sprintf_s(buffer, sizeof(buffer), "%04d-%02d-%02d %02d:%02d:%02d.%03d",
              st.wYear, st.wMonth, st.wDay,
              st.wHour, st.wMinute, st.wSecond, st.wMilliseconds);

    return std::string(buffer);
}

std::string SimpleSocketInterface::SafeCStringToUTF8(const CString& str) {
    if (str.IsEmpty()) {
        return "";
    }

    // 简化版UTF-8转换
    CT2A utf8String(str, CP_UTF8);
    return std::string(utf8String);
}

std::string SimpleSocketInterface::BuildEventMessage(const std::string& eventType, const std::string& properties) {
    // 使用JsonCpp构建事件消息
    Json::Value message;
    Json::Value services(Json::arrayValue);
    Json::Value eventService;

    eventService["service_id"] = Json::Value("EventService");
    eventService["event_type"] = Json::Value(eventType);
    eventService["event_time"] = Json::Value(GetCurrentTimestamp());

    // 解析properties字符串为Json::Value对象
    Json::Reader reader;
    Json::Value propertiesValue;
    if (reader.parse(properties, propertiesValue)) {
        eventService["properties"] = propertiesValue;
    } else {
        // 如果解析失败，创建空对象
        eventService["properties"] = Json::Value(Json::objectValue);
    }

    services.append(eventService);
    message["services"] = services;

    Json::FastWriter writer;
    std::string messageStr = writer.write(message);
    // 移除FastWriter添加的换行符
    if (!messageStr.empty() && messageStr.back() == '\n') {
        messageStr.pop_back();
    }

    return messageStr;
}

// 数据收集方法 - 使用JsonCpp从CDat获取真实数据
std::string SimpleSocketInterface::CollectAllDataPoints() {
    Json::Value root;

    // 进入临界区保护数据访问
    EnterCriticalSection(&CDat::m_csDataCollection);

    try {
        // A系列 - 设备运行状态
        if (CDat::m_mapDataCollection.find(_T("正常生产")) != CDat::m_mapDataCollection.end()) {
            bool normalProduction = (CDat::m_mapDataCollection[_T("正常生产")].pDat->I() == 1);
            root["A00002"] = normalProduction;
        } else {
            root["A00002"] = false;
        }

        if (CDat::m_mapDataCollection.find(_T("运行暂停")) != CDat::m_mapDataCollection.end()) {
            bool runPause = (CDat::m_mapDataCollection[_T("运行暂停")].pDat->I() == 1);
            root["A00003"] = runPause;
        } else {
            root["A00003"] = false;
        }

        if (CDat::m_mapDataCollection.find(_T("设备故障")) != CDat::m_mapDataCollection.end()) {
            bool deviceFault = (CDat::m_mapDataCollection[_T("设备故障")].pDat->I() == 1);
            root["A00004"] = deviceFault;
        } else {
            root["A00004"] = false;
        }

        if (CDat::m_mapDataCollection.find(_T("待机状态")) != CDat::m_mapDataCollection.end()) {
            bool standbyStatus = (CDat::m_mapDataCollection[_T("待机状态")].pDat->I() == 1);
            root["A00006"] = standbyStatus;
        } else {
            root["A00006"] = false;
        }

        // 机型程序名
        if (CDat::m_mapDataCollection.find(_T("机型程序名")) != CDat::m_mapDataCollection.end()) {
            CString programName = CDat::m_mapDataCollection[_T("机型程序名")].pDat->Value();
            if (!programName.IsEmpty() && programName != "NULL") {
                root["A00010"] = Json::Value(SafeCStringToUTF8(programName));
            } else {
                root["A00010"] = Json::Value("DefaultProgram");
            }
        } else {
            root["A00010"] = Json::Value("DefaultProgram");
        }

        // 运行周期
        if (CDat::m_mapDataCollection.find(_T("运行周期")) != CDat::m_mapDataCollection.end()) {
            CString cycleTime = CDat::m_mapDataCollection[_T("运行周期")].pDat->Value();
            if (!cycleTime.IsEmpty() && cycleTime != "NULL") {
                double cycleValue = _ttof(cycleTime);
                root["A00014"] = cycleValue;
            } else {
                root["A00014"] = 0.0;
            }
        } else {
            root["A00014"] = 0.0;
        }

        // 累计产能
        if (CDat::m_mapDataCollection.find(_T("累计产能")) != CDat::m_mapDataCollection.end()) {
            int totalOutput = CDat::m_mapDataCollection[_T("累计产能")].pDat->I();
            root["A00015"] = totalOutput;
        } else {
            root["A00015"] = 0;
        }

    } catch (...) {
        LogError(_T("获取A系列数据点值时发生异常"));
    }

    // 离开临界区
    LeaveCriticalSection(&CDat::m_csDataCollection);

    // 硬件资源监控 (A00020-A00030) - 实时获取系统信息
    AddHardwareMonitorData(root);

    // 重新进入临界区获取其他数据
    EnterCriticalSection(&CDat::m_csDataCollection);

    try {
        // C系列 - 生产追溯参数
        if (CDat::m_mapDataCollection.find(_T("主板SN号")) != CDat::m_mapDataCollection.end()) {
            CString mainboardSN = CDat::m_mapDataCollection[_T("主板SN号")].pDat->Value();
            if (!mainboardSN.IsEmpty() && mainboardSN != "NULL") {
                root["C00001"] = Json::Value(SafeCStringToUTF8(mainboardSN));
            } else {
                root["C00001"] = Json::Value("NULL");
            }
        } else {
            root["C00001"] = Json::Value("NULL");
        }

        // 设备资产编码 - 使用配置中的设备ID
        root["C00002"] = Json::Value("A320021760");

        if (CDat::m_mapDataCollection.find(_T("轨道号")) != CDat::m_mapDataCollection.end()) {
            int trackNo = CDat::m_mapDataCollection[_T("轨道号")].pDat->I();
            root["C00003"] = trackNo;
        } else {
            root["C00003"] = 1;
        }

        if (CDat::m_mapDataCollection.find(_T("面别")) != CDat::m_mapDataCollection.end()) {
            CString side = CDat::m_mapDataCollection[_T("面别")].pDat->Value();
            if (!side.IsEmpty() && side != "NULL") {
                root["C00004"] = Json::Value(SafeCStringToUTF8(side));
            } else {
                root["C00004"] = Json::Value("T");
            }
        } else {
            root["C00004"] = Json::Value("T");
        }

        if (CDat::m_mapDataCollection.find(_T("程序名")) != CDat::m_mapDataCollection.end()) {
            CString programName = CDat::m_mapDataCollection[_T("程序名")].pDat->Value();
            if (!programName.IsEmpty() && programName != "NULL") {
                root["C00005"] = Json::Value(SafeCStringToUTF8(programName));
            } else {
                root["C00005"] = Json::Value("DefaultProgram");
            }
        } else {
            root["C00005"] = Json::Value("DefaultProgram");
        }

    } catch (...) {
        LogError(_T("获取C系列数据点值时发生异常"));
    }

    // 离开临界区
    LeaveCriticalSection(&CDat::m_csDataCollection);

    // 添加B系列测高数据
    AddMeasurementData(root);

    // 使用JsonCpp的FastWriter生成JSON字符串
    Json::FastWriter writer;
    std::string jsonString = writer.write(root);

    // 移除FastWriter添加的换行符
    if (!jsonString.empty() && jsonString.back() == '\n') {
        jsonString.pop_back();
    }

    return jsonString;
}

// 硬件监控方法实现 - 使用JsonCpp（保持兼容性，推荐使用AddHardwareMonitorData）
std::string SimpleSocketInterface::GetHardwareMonitorData() {
    // 调试输出：确认方法被调用
    LogInfo(_T("开始获取硬件监控数据"));

    Json::Value hardwareData;

    // A00020: 内存容量(MB)
    DWORD memCapacity = GetMemoryCapacityMB();
    hardwareData["A00020"] = static_cast<int>(memCapacity);

    // 调试输出：显示内存容量
    CString debugMsg;
    debugMsg.Format(_T("内存容量: %lu MB"), memCapacity);
    LogInfo(debugMsg);

    // A00021: 磁盘容量(GB) - 获取C盘总容量
    DWORD diskCapacity = GetDiskCapacityGB(_T("C:"));
    hardwareData["A00021"] = static_cast<int>(diskCapacity);

    // A00022-A00024: 各盘容量
    hardwareData["A00022"] = static_cast<int>(GetDiskCapacityGB(_T("C:")));
    hardwareData["A00023"] = static_cast<int>(GetDiskCapacityGB(_T("D:")));
    hardwareData["A00024"] = static_cast<int>(GetDiskCapacityGB(_T("E:")));

    // A00025-A00028: 各盘剩余容量
    hardwareData["A00025"] = static_cast<int>(GetDiskFreeSpaceGB(_T("C:")));
    hardwareData["A00026"] = static_cast<int>(GetDiskFreeSpaceGB(_T("C:")));
    hardwareData["A00027"] = static_cast<int>(GetDiskFreeSpaceGB(_T("D:")));
    hardwareData["A00028"] = static_cast<int>(GetDiskFreeSpaceGB(_T("E:")));

    // A00029: CPU利用率(%)
    float cpuUsage = GetCpuUsage();
    hardwareData["A00029"] = cpuUsage;

    // A00030: 内存利用率(%)
    float memUsage = GetMemoryUsage();
    hardwareData["A00030"] = memUsage;

    Json::FastWriter writer;
    std::string jsonStr = writer.write(hardwareData);
    // 移除FastWriter添加的换行符
    if (!jsonStr.empty() && jsonStr.back() == '\n') {
        jsonStr.pop_back();
    }

    // 移除外层的大括号，返回键值对字符串（保持原有接口兼容性）
    if (jsonStr.length() > 2 && jsonStr.front() == '{' && jsonStr.back() == '}') {
        jsonStr = jsonStr.substr(1, jsonStr.length() - 2) + ",";
    }

    return jsonStr;
}

// 使用JsonCpp添加硬件监控数据
void SimpleSocketInterface::AddHardwareMonitorData(Json::Value& root) {
    try {
        // A00020: 内存容量(MB)
        DWORD memCapacity = GetMemoryCapacityMB();
        root["A00020"] = static_cast<int>(memCapacity);

        // A00021: 磁盘容量(GB) - 获取C盘总容量
        DWORD diskCapacity = GetDiskCapacityGB(_T("C:"));
        root["A00021"] = static_cast<int>(diskCapacity);

        // A00022-A00024: 各盘容量
        root["A00022"] = static_cast<int>(GetDiskCapacityGB(_T("C:")));
        root["A00023"] = static_cast<int>(GetDiskCapacityGB(_T("D:")));
        root["A00024"] = static_cast<int>(GetDiskCapacityGB(_T("E:")));

        // A00025-A00028: 各盘剩余容量
        root["A00025"] = static_cast<int>(GetDiskFreeSpaceGB(_T("C:")));
        root["A00026"] = static_cast<int>(GetDiskFreeSpaceGB(_T("C:")));
        root["A00027"] = static_cast<int>(GetDiskFreeSpaceGB(_T("D:")));
        root["A00028"] = static_cast<int>(GetDiskFreeSpaceGB(_T("E:")));

        // A00029: CPU利用率(%)
        float cpuUsage = GetCpuUsage();
        root["A00029"] = cpuUsage;

        // A00030: 内存利用率(%)
        float memUsage = GetMemoryUsage();
        root["A00030"] = memUsage;

        LogInfo(_T("硬件监控数据添加完成"));

    } catch (...) {
        LogError(_T("添加硬件监控数据时发生异常"));
    }
}

// 使用JsonCpp添加测量数据
void SimpleSocketInterface::AddMeasurementData(Json::Value& root) {
    // 进入临界区保护数据访问
    EnterCriticalSection(&CDat::m_csDataCollection);

    try {
        // B系列 - 测高数据
        if (CDat::m_mapDataCollection.find(_T("1轨测高值1")) != CDat::m_mapDataCollection.end()) {
            double height1 = CDat::m_mapDataCollection[_T("1轨测高值1")].pDat->D();
            root["B40008"] = height1;
        } else {
            root["B40008"] = 0.0;
        }

        if (CDat::m_mapDataCollection.find(_T("1轨测高值2")) != CDat::m_mapDataCollection.end()) {
            double height2 = CDat::m_mapDataCollection[_T("1轨测高值2")].pDat->D();
            root["B40009"] = height2;
        } else {
            root["B40009"] = 0.0;
        }

        if (CDat::m_mapDataCollection.find(_T("1轨测高值3")) != CDat::m_mapDataCollection.end()) {
            double height3 = CDat::m_mapDataCollection[_T("1轨测高值3")].pDat->D();
            root["B40010"] = height3;
        } else {
            root["B40010"] = 0.0;
        }

        if (CDat::m_mapDataCollection.find(_T("1轨测高值4")) != CDat::m_mapDataCollection.end()) {
            double height4 = CDat::m_mapDataCollection[_T("1轨测高值4")].pDat->D();
            root["B40011"] = height4;
        } else {
            root["B40011"] = 0.0;
        }

        if (CDat::m_mapDataCollection.find(_T("2轨测高值1")) != CDat::m_mapDataCollection.end()) {
            double height5 = CDat::m_mapDataCollection[_T("2轨测高值1")].pDat->D();
            root["B40012"] = height5;
        } else {
            root["B40012"] = 0.0;
        }

        if (CDat::m_mapDataCollection.find(_T("2轨测高值2")) != CDat::m_mapDataCollection.end()) {
            double height6 = CDat::m_mapDataCollection[_T("2轨测高值2")].pDat->D();
            root["B40013"] = height6;
        } else {
            root["B40013"] = 0.0;
        }

        if (CDat::m_mapDataCollection.find(_T("2轨测高值3")) != CDat::m_mapDataCollection.end()) {
            double height7 = CDat::m_mapDataCollection[_T("2轨测高值3")].pDat->D();
            root["B40014"] = height7;
        } else {
            root["B40014"] = 0.0;
        }

        if (CDat::m_mapDataCollection.find(_T("2轨测高值4")) != CDat::m_mapDataCollection.end()) {
            double height8 = CDat::m_mapDataCollection[_T("2轨测高值4")].pDat->D();
            root["B40015"] = height8;
        } else {
            root["B40015"] = 0.0;
        }

        if (CDat::m_mapDataCollection.find(_T("测高阈值")) != CDat::m_mapDataCollection.end()) {
            double heightThreshold = CDat::m_mapDataCollection[_T("测高阈值")].pDat->D();
            root["B40016"] = heightThreshold;
        } else {
            root["B40016"] = 0.0;
        }

        LogInfo(_T("测量数据添加完成"));

    } catch (...) {
        LogError(_T("添加测量数据时发生异常"));
    }

    // 离开临界区
    LeaveCriticalSection(&CDat::m_csDataCollection);
}

DWORD SimpleSocketInterface::GetMemoryCapacityMB() {
    MEMORYSTATUSEX memStatus;
    memStatus.dwLength = sizeof(memStatus);

    if (GlobalMemoryStatusEx(&memStatus)) {
        // 转换为MB
        DWORD result = static_cast<DWORD>(memStatus.ullTotalPhys / (1024 * 1024));
        if (result == 0) {
            // 如果获取失败，返回默认值
            return 16384; // 16GB
        }
        return result;
    }

    // 如果API调用失败，返回默认值
    return 16384; // 16GB
}

DWORD SimpleSocketInterface::GetDiskCapacityGB(const CString& drive) {
    ULARGE_INTEGER freeBytesAvailable;
    ULARGE_INTEGER totalNumberOfBytes;
    ULARGE_INTEGER totalNumberOfFreeBytes;

    if (GetDiskFreeSpaceEx(drive, &freeBytesAvailable, &totalNumberOfBytes, &totalNumberOfFreeBytes)) {
        // 转换为GB
        DWORD result = static_cast<DWORD>(totalNumberOfBytes.QuadPart / (1024 * 1024 * 1024));
        if (result > 0) {
            return result;
        }
    }

    // 如果获取失败，返回默认值
    if (drive == _T("C:")) return 500;  // C盘默认500GB
    if (drive == _T("D:")) return 1000; // D盘默认1TB
    if (drive == _T("E:")) return 2000; // E盘默认2TB
    return 100; // 其他盘默认100GB
}

DWORD SimpleSocketInterface::GetDiskFreeSpaceGB(const CString& drive) {
    ULARGE_INTEGER freeBytesAvailable;
    ULARGE_INTEGER totalNumberOfBytes;
    ULARGE_INTEGER totalNumberOfFreeBytes;

    if (GetDiskFreeSpaceEx(drive, &freeBytesAvailable, &totalNumberOfBytes, &totalNumberOfFreeBytes)) {
        // 转换为GB
        DWORD result = static_cast<DWORD>(totalNumberOfFreeBytes.QuadPart / (1024 * 1024 * 1024));
        if (result > 0) {
            return result;
        }
    }

    // 如果获取失败，返回默认值
    if (drive == _T("C:")) return 200;  // C盘默认剩余200GB
    if (drive == _T("D:")) return 800;  // D盘默认剩余800GB
    if (drive == _T("E:")) return 1500; // E盘默认剩余1.5TB
    return 50; // 其他盘默认剩余50GB
}

float SimpleSocketInterface::GetCpuUsage() {
    // 简化版CPU使用率获取
    // 在实际应用中，需要使用PDH (Performance Data Helper) API
    // 这里返回一个模拟值，实际项目中应该实现真实的CPU监控

    static DWORD lastIdleTime = 0;
    static DWORD lastKernelTime = 0;
    static DWORD lastUserTime = 0;

    FILETIME idleTime, kernelTime, userTime;
    if (GetSystemTimes(&idleTime, &kernelTime, &userTime)) {
        DWORD currentIdleTime = idleTime.dwLowDateTime;
        DWORD currentKernelTime = kernelTime.dwLowDateTime;
        DWORD currentUserTime = userTime.dwLowDateTime;

        if (lastIdleTime != 0) {
            DWORD idleDiff = currentIdleTime - lastIdleTime;
            DWORD kernelDiff = currentKernelTime - lastKernelTime;
            DWORD userDiff = currentUserTime - lastUserTime;
            DWORD totalDiff = kernelDiff + userDiff;

            if (totalDiff > 0) {
                float usage = 100.0f * (1.0f - (float)idleDiff / (float)totalDiff);
                lastIdleTime = currentIdleTime;
                lastKernelTime = currentKernelTime;
                lastUserTime = currentUserTime;
                return (usage < 0) ? 0.0f : ((usage > 100.0f) ? 100.0f : usage);
            }
        }

        lastIdleTime = currentIdleTime;
        lastKernelTime = currentKernelTime;
        lastUserTime = currentUserTime;
    }

    // 返回默认值
    return 15.5f;
}

float SimpleSocketInterface::GetMemoryUsage() {
    MEMORYSTATUSEX memStatus;
    memStatus.dwLength = sizeof(memStatus);

    if (GlobalMemoryStatusEx(&memStatus)) {
        DWORDLONG usedMemory = memStatus.ullTotalPhys - memStatus.ullAvailPhys;
        float usage = (float)usedMemory * 100.0f / (float)memStatus.ullTotalPhys;
        return usage;
    }

    return 0.0f;
}

