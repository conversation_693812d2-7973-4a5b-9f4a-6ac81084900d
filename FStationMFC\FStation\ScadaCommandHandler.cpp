#include "StdAfx.h"
#include "ScadaCommandHandler.h"
#include "Sys.h"
#include "LogicMgr.h"
#include "SimpleSocketInterface.h"

// VS2010兼容性：确保time函数可用
#include <time.h>
#include <sstream>

// 静态成员初始化
CScadaCommandHandler* CScadaCommandHandler::m_pInstance = NULL;
CRITICAL_SECTION CScadaCommandHandler::m_instanceMutex;
bool CScadaCommandHandler::m_instanceMutexInitialized = false;

CScadaCommandHandler* CScadaCommandHandler::GetInstance()
{
    if (!m_instanceMutexInitialized) {
        InitializeCriticalSection(&m_instanceMutex);
        m_instanceMutexInitialized = true;
    }
    
    EnterCriticalSection(&m_instanceMutex);
    if (m_pInstance == NULL) {
        m_pInstance = new CScadaCommandHandler();
    }
    LeaveCriticalSection(&m_instanceMutex);
    
    return m_pInstance;
}

void CScadaCommandHandler::DestroyInstance()
{
    if (m_instanceMutexInitialized) {
        EnterCriticalSection(&m_instanceMutex);
        if (m_pInstance != NULL) {
            delete m_pInstance;
            m_pInstance = NULL;
        }
        LeaveCriticalSection(&m_instanceMutex);
        
        DeleteCriticalSection(&m_instanceMutex);
        m_instanceMutexInitialized = false;
    }
}

CScadaCommandHandler::CScadaCommandHandler()
    : m_bInitialized(false)
    , m_currentCommandType(COMMAND_UNKNOWN)
{
}

CScadaCommandHandler::~CScadaCommandHandler()
{
    Cleanup();
}

bool CScadaCommandHandler::Initialize()
{
    if (m_bInitialized) {
        return true;
    }
    
    try {
        // 初始化命令类型映射
        m_commandTypeMap["COMMAND_SN_DELIVER"] = COMMAND_SN_DELIVER;
        m_commandTypeMap["COMMAND_BOP_DELIVER"] = COMMAND_BOP_DELIVER;
        m_commandTypeMap["COMMAND_PAUSE"] = COMMAND_PAUSE;
        m_commandTypeMap["COMMAND_PRODUCTION"] = COMMAND_PRODUCTION;
        m_commandTypeMap["COMMAND_MQTT_CONFIG_DELIVER"] = COMMAND_MQTT_CONFIG_DELIVER;
        
        m_bInitialized = true;
        LogInfo(_T("SCADA命令处理器初始化成功"));
        
        return true;
    }
    catch (...) {
        LogError(_T("SCADA命令处理器初始化失败"));
        return false;
    }
}

void CScadaCommandHandler::Cleanup()
{
    if (m_bInitialized) {
        m_commandTypeMap.clear();
        m_bInitialized = false;
        LogInfo(_T("SCADA命令处理器清理完成"));
    }
}

CScadaCommandHandler::CommandResult CScadaCommandHandler::ProcessCommand(const CString& commandJson, const CString& requestId)
{
    if (!m_bInitialized) {
        return CommandResult(-100, "命令处理器未初始化");
    }
    
    try {
        // 转换CString到std::string
        std::string jsonStr = CStringToStdString(commandJson);
        std::string reqId = CStringToStdString(requestId);
        
        // 解析JSON
        Json::Reader reader;
        Json::Value root;
        if (!reader.parse(jsonStr, root)) {
            LogError(_T("命令JSON解析失败: ") + commandJson);
            return CommandResult(-101, "命令JSON格式错误");
        }
        
        // 提取命令信息
        if (!root.isMember("command_name") || !root["command_name"].isString()) {
            return CommandResult(-102, "缺少command_name字段");
        }
        
        std::string commandName = root["command_name"].asString();
        CommandType cmdType = ParseCommandType(commandName);
        
        if (cmdType == COMMAND_UNKNOWN) {
            return CommandResult(-103, "不支持的命令类型: " + commandName);
        }
        
        // 设置当前命令信息
        m_currentRequestId = reqId;
        m_currentCommandType = cmdType;
        m_currentCommandData = root;
        
        // 提取properties
        Json::Value properties;
        if (root.isMember("properties") && root["properties"].isObject()) {
            properties = root["properties"];
        }
        
        LogInfo(StdStringToCString("开始处理命令: " + commandName + ", RequestId: " + reqId));
        
        // 根据命令类型分发处理
        CommandResult result;
        switch (cmdType) {
            case COMMAND_SN_DELIVER:
                result = HandleSnDeliverCommand(properties);
                break;
            case COMMAND_BOP_DELIVER:
                result = HandleBopDeliverCommand(properties);
                break;
            case COMMAND_PAUSE:
                result = HandlePauseCommand(properties);
                break;
            case COMMAND_PRODUCTION:
                result = HandleProductionCommand(properties);
                break;
            case COMMAND_MQTT_CONFIG_DELIVER:
                result = HandleMqttConfigDeliverCommand(properties);
                break;
            default:
                result = CommandResult(-104, "未实现的命令处理: " + commandName);
                break;
        }
        
        // 记录处理结果
        if (result.resultCode == 0) {
            LogInfo(StdStringToCString("命令执行成功: " + commandName + ", RequestId: " + reqId));
        } else {
            LogError(StdStringToCString("命令执行失败: " + commandName + ", RequestId: " + reqId + 
                                      ", 错误: " + result.resultMessage));
        }
        
        return result;
        
    } catch (const std::exception& e) {
        std::string errorMsg = "命令处理异常: ";
        errorMsg += e.what();
        LogError(StdStringToCString(errorMsg));
        return CommandResult(-199, errorMsg);
    } catch (...) {
        LogError(_T("命令处理发生未知异常"));
        return CommandResult(-199, "命令处理发生未知异常");
    }
}

CScadaCommandHandler::CommandResult CScadaCommandHandler::ProcessSocketCommand(const std::string& socketMessage)
{
    try {
        // 解析Socket消息
        Json::Reader reader;
        Json::Value socketRoot;
        if (!reader.parse(socketMessage, socketRoot)) {
            return CommandResult(-201, "Socket消息JSON解析失败");
        }
        
        // 提取requestId
        std::string requestId;
        if (socketRoot.isMember("requestId") && socketRoot["requestId"].isString()) {
            requestId = socketRoot["requestId"].asString();
        }
        
        // 提取data字段（包含SCADA命令）
        if (!socketRoot.isMember("data") || !socketRoot["data"].isString()) {
            return CommandResult(-202, "Socket消息缺少data字段");
        }
        
        std::string scadaCommandJson = socketRoot["data"].asString();
        
        // 调用SCADA命令处理
        return ProcessCommand(StdStringToCString(scadaCommandJson), StdStringToCString(requestId));
        
    } catch (...) {
        return CommandResult(-299, "Socket命令处理异常");
    }
}

CScadaCommandHandler::CommandType CScadaCommandHandler::ParseCommandType(const std::string& commandName)
{
    std::map<std::string, CommandType>::iterator it = m_commandTypeMap.find(commandName);
    if (it != m_commandTypeMap.end()) {
        return it->second;
    }
    return COMMAND_UNKNOWN;
}

CScadaCommandHandler::CommandResult CScadaCommandHandler::HandleSnDeliverCommand(const Json::Value& properties)
{
    // 验证参数
    std::string errorMsg;
    if (!ValidateSnDeliverParams(properties, errorMsg)) {
        return CommandResult(-301, errorMsg);
    }
    
    try {
        // 提取参数
        std::string sn = properties["sn"].asString();
        std::string productionModel = properties["production_model"].asString();
        
        // 执行SN下发业务逻辑
        if (!SetCurrentSN(sn, productionModel)) {
            return CommandResult(-302, "SN下发失败");
        }
        
        // 构建成功响应
        Json::Value responseData;
        responseData["sn"] = sn;
        responseData["production_model"] = productionModel;
        responseData["status"] = "delivered";
        
        CommandResult result(0, "SN下发成功");
        result.responseData = JsonValueToString(responseData);
        return result;
        
    } catch (...) {
        return CommandResult(-399, "SN下发处理异常");
    }
}

CScadaCommandHandler::CommandResult CScadaCommandHandler::HandleBopDeliverCommand(const Json::Value& properties)
{
    // 验证参数
    std::string errorMsg;
    if (!ValidateBopDeliverParams(properties, errorMsg)) {
        return CommandResult(-401, errorMsg);
    }
    
    try {
        // 执行转产业务逻辑
        if (!SetProductionParameters(properties)) {
            return CommandResult(-402, "转产参数设置失败");
        }
        
        CommandResult result(0, "转产参数下发成功");
        result.responseData = BuildSuccessResponse("转产参数已应用");
        return result;
        
    } catch (...) {
        return CommandResult(-499, "转产处理异常");
    }
}

CScadaCommandHandler::CommandResult CScadaCommandHandler::HandlePauseCommand(const Json::Value& properties)
{
    try {
        std::string reason = "平台暂停指令";
        if (properties.isMember("reason") && properties["reason"].isString()) {
            reason = properties["reason"].asString();
        }
        
        // 执行暂停业务逻辑
        if (!PauseProduction(reason)) {
            return CommandResult(-502, "暂停生产失败");
        }
        
        CommandResult result(0, "生产已暂停");
        result.responseData = BuildSuccessResponse("生产已暂停: " + reason);
        return result;
        
    } catch (...) {
        return CommandResult(-599, "暂停处理异常");
    }
}

CScadaCommandHandler::CommandResult CScadaCommandHandler::HandleProductionCommand(const Json::Value& properties)
{
    try {
        // 执行恢复生产业务逻辑
        if (!ResumeProduction()) {
            return CommandResult(-602, "恢复生产失败");
        }
        
        CommandResult result(0, "生产已恢复");
        result.responseData = BuildSuccessResponse("生产已恢复");
        return result;
        
    } catch (...) {
        return CommandResult(-699, "恢复生产处理异常");
    }
}

CScadaCommandHandler::CommandResult CScadaCommandHandler::HandleMqttConfigDeliverCommand(const Json::Value& properties)
{
    // 验证参数
    std::string errorMsg;
    if (!ValidateMqttConfigParams(properties, errorMsg)) {
        return CommandResult(-701, errorMsg);
    }
    
    try {
        // 执行MQTT配置更新
        if (!UpdateMqttConfig(properties)) {
            return CommandResult(-702, "MQTT配置更新失败");
        }
        
        CommandResult result(0, "MQTT配置更新成功");
        result.responseData = BuildSuccessResponse("MQTT配置已更新");
        return result;
        
    } catch (...) {
        return CommandResult(-799, "MQTT配置处理异常");
    }
}

// 参数验证方法实现
bool CScadaCommandHandler::ValidateSnDeliverParams(const Json::Value& properties, std::string& errorMsg)
{
    if (!properties.isMember("sn") || !properties["sn"].isString()) {
        errorMsg = "缺少必需参数: sn";
        return false;
    }

    if (!properties.isMember("production_model") || !properties["production_model"].isString()) {
        errorMsg = "缺少必需参数: production_model";
        return false;
    }

    std::string sn = properties["sn"].asString();
    if (sn.empty()) {
        errorMsg = "SN不能为空";
        return false;
    }

    return true;
}

bool CScadaCommandHandler::ValidateBopDeliverParams(const Json::Value& properties, std::string& errorMsg)
{
    // BOP参数验证相对宽松，允许各种配置参数
    if (properties.isNull() || !properties.isObject()) {
        errorMsg = "转产参数必须是对象类型";
        return false;
    }

    return true;
}

bool CScadaCommandHandler::ValidatePauseParams(const Json::Value& properties, std::string& errorMsg)
{
    // 暂停命令参数是可选的
    return true;
}

bool CScadaCommandHandler::ValidateProductionParams(const Json::Value& properties, std::string& errorMsg)
{
    // 恢复生产命令参数是可选的
    return true;
}

bool CScadaCommandHandler::ValidateMqttConfigParams(const Json::Value& properties, std::string& errorMsg)
{
    if (properties.isNull() || !properties.isObject()) {
        errorMsg = "MQTT配置参数必须是对象类型";
        return false;
    }

    return true;
}

// 工具方法实现
std::string CScadaCommandHandler::JsonValueToString(const Json::Value& value)
{
    Json::FastWriter writer;
    return writer.write(value);
}

CString CScadaCommandHandler::StdStringToCString(const std::string& str)
{
    return CString(str.c_str());
}

std::string CScadaCommandHandler::CStringToStdString(const CString& str)
{
    return std::string(CT2A(str));
}

void CScadaCommandHandler::LogInfo(const CString& message)
{
    REPORT(_T("[SCADA命令] ") + message, emLogLevelNormal);
}

void CScadaCommandHandler::LogWarning(const CString& message)
{
    REPORT(_T("[SCADA命令] ") + message, emLogLevelWarn);
}

void CScadaCommandHandler::LogError(const CString& message)
{
    REPORT(_T("[SCADA命令] ") + message, emLogLevelError);
}

std::string CScadaCommandHandler::BuildSuccessResponse(const std::string& message)
{
    Json::Value response;
    response["result_code"] = 0;
    response["result_message"] = message;
    response["timestamp"] = Json::Value(static_cast<int>(time(NULL)));

    return JsonValueToString(response);
}

std::string CScadaCommandHandler::BuildErrorResponse(int errorCode, const std::string& errorMessage)
{
    Json::Value response;
    response["result_code"] = errorCode;
    response["result_message"] = errorMessage;
    response["timestamp"] = Json::Value(static_cast<int>(time(NULL)));

    return JsonValueToString(response);
}

// 业务逻辑接口实现
bool CScadaCommandHandler::SetCurrentSN(const std::string& sn, const std::string& productionModel)
{
    try {
        // 设置当前SN到系统变量
        //CString csSN = StdStringToCString(sn);
        //CString csModel = StdStringToCString(productionModel);

        //// 更新系统变量
        //if (g_pMachine->m_mapParam.find(_T("当前SN")) != g_pMachine->m_mapParam.end()) {
        //    *g_pMachine->m_mapParam[_T("当前SN")] = csSN;
        //}
        //if (g_pMachine->m_mapParam.find(_T("当前产品型号")) != g_pMachine->m_mapParam.end()) {
        //    *g_pMachine->m_mapParam[_T("当前产品型号")] = csModel;
        //}

        //LogInfo(_T("SN下发成功: ") + csSN + _T(", 型号: ") + csModel);

        //// 触发SN入站事件（如果需要）
        //SimpleSocketInterface::SendSnInEvent(csSN, csModel);

        return true;
    }
    catch (...) {
        LogError(_T("设置当前SN失败"));
        return false;
    }
}

bool CScadaCommandHandler::SetProductionParameters(const Json::Value& parameters)
{
    try {
        // 遍历参数并设置到系统变量
        Json::Value::Members members = parameters.getMemberNames();
        size_t i;
        for (i = 0; i < members.size(); ++i) {
            std::string key = members[i];
            Json::Value value = parameters[key];

            // 根据参数类型设置系统变量
            CString csKey = StdStringToCString(key);
            if (value.isString()) {
                // 使用CData的赋值操作符
                if (g_pMachine->m_mapParam.find(csKey) != g_pMachine->m_mapParam.end()) {
                    *g_pMachine->m_mapParam[csKey] = StdStringToCString(value.asString());
                }
            } else if (value.isInt()) {
                if (g_pMachine->m_mapParam.find(csKey) != g_pMachine->m_mapParam.end()) {
                    *g_pMachine->m_mapParam[csKey] = value.asInt();
                }
            } else if (value.isDouble()) {
                if (g_pMachine->m_mapParam.find(csKey) != g_pMachine->m_mapParam.end()) {
                    *g_pMachine->m_mapParam[csKey] = value.asDouble();
                }
            } else if (value.isBool()) {
                if (g_pMachine->m_mapParam.find(csKey) != g_pMachine->m_mapParam.end()) {
                    *g_pMachine->m_mapParam[csKey] = value.asBool();
                }
            }

            LogInfo(StdStringToCString("设置生产参数: " + key + " = " + JsonValueToString(value)));
        }

        LogInfo(_T("转产参数设置完成"));
        return true;
    }
    catch (...) {
        LogError(_T("设置生产参数失败"));
        return false;
    }
}

bool CScadaCommandHandler::PauseProduction(const std::string& reason)
{
    try {
        // 设置暂停标志
        if (g_pMachine->m_mapParam.find(_T("生产暂停")) != g_pMachine->m_mapParam.end()) {
            *g_pMachine->m_mapParam[_T("生产暂停")] = true;
        }
        if (g_pMachine->m_mapParam.find(_T("暂停原因")) != g_pMachine->m_mapParam.end()) {
            *g_pMachine->m_mapParam[_T("暂停原因")] = StdStringToCString(reason);
        }

        // 停止逻辑管理器（如果正在运行）
        if (CLogicMgr::isRunning()) {
            LogInfo(_T("正在停止生产逻辑..."));
            try {
                CLogicMgr::Stop();
                LogInfo(_T("生产逻辑已停止"));
            } catch (...) {
                LogError(_T("停止生产逻辑时发生异常，但继续执行暂停流程"));
            }
        }

        LogInfo(StdStringToCString("生产已暂停: " + reason));

        // 注意：不发送事件，只通过COMMAND_RESPONSE响应

        return true;
    }
    catch (...) {
        LogError(_T("暂停生产失败"));
        return false;
    }
}

bool CScadaCommandHandler::ResumeProduction()
{
    try {
        // 清除暂停标志
        if (g_pMachine->m_mapParam.find(_T("生产暂停")) != g_pMachine->m_mapParam.end()) {
            *g_pMachine->m_mapParam[_T("生产暂停")] = false;
        }
        if (g_pMachine->m_mapParam.find(_T("暂停原因")) != g_pMachine->m_mapParam.end()) {
            *g_pMachine->m_mapParam[_T("暂停原因")] = _T("");
        }

        // 启动逻辑管理器
        if (!CLogicMgr::isRunning()) {
            CLogicMgr::Run();
            LogInfo(_T("生产逻辑已启动"));
        }

        LogInfo(_T("生产已恢复"));
        return true;
    }
    catch (...) {
        LogError(_T("恢复生产失败"));
        return false;
    }
}

bool CScadaCommandHandler::UpdateMqttConfig(const Json::Value& config)
{
    try {
        // 更新MQTT相关配置
        if (config.isMember("report_interval") && config["report_interval"].isInt()) {
            int interval = config["report_interval"].asInt();
            if (g_pMachine->m_mapParam.find(_T("MQTT上报间隔")) != g_pMachine->m_mapParam.end()) {
                *g_pMachine->m_mapParam[_T("MQTT上报间隔")] = interval;
            }

            // VS2010兼容：使用stringstream代替std::to_string
            std::ostringstream oss;
            oss << "更新MQTT上报间隔: " << interval << "秒";
            LogInfo(StdStringToCString(oss.str()));
        }

        if (config.isMember("enable_mqtt") && config["enable_mqtt"].isBool()) {
            bool enable = config["enable_mqtt"].asBool();
            if (g_pMachine->m_mapParam.find(_T("MQTT功能启用")) != g_pMachine->m_mapParam.end()) {
                *g_pMachine->m_mapParam[_T("MQTT功能启用")] = enable;
            }
            LogInfo(StdStringToCString("更新MQTT功能启用状态: " + std::string(enable ? "启用" : "禁用")));
        }

        LogInfo(_T("MQTT配置更新完成"));
        return true;
    }
    catch (...) {
        LogError(_T("更新MQTT配置失败"));
        return false;
    }
}

// 状态检查方法
bool CScadaCommandHandler::IsProductionRunning()
{
    bool isPaused = false;
    if (g_pMachine->m_mapParam.find(_T("生产暂停")) != g_pMachine->m_mapParam.end()) {
        isPaused = g_pMachine->m_mapParam[_T("生产暂停")]->B();
    }
    return CLogicMgr::isRunning() && !isPaused;
}

bool CScadaCommandHandler::IsProductionPaused()
{
    if (g_pMachine->m_mapParam.find(_T("生产暂停")) != g_pMachine->m_mapParam.end()) {
        return g_pMachine->m_mapParam[_T("生产暂停")]->B();
    }
    return false;
}

std::string CScadaCommandHandler::GetCurrentProductionModel()
{
    if (g_pMachine->m_mapParam.find(_T("当前产品型号")) != g_pMachine->m_mapParam.end()) {
        CString csModel = g_pMachine->m_mapParam[_T("当前产品型号")]->S();
        return CStringToStdString(csModel);
    }
    return "";
}

// 全局访问函数
CScadaCommandHandler* GetScadaCommandHandler()
{
    return CScadaCommandHandler::GetInstance();
}

// Socket命令处理回调函数实现
int OnSocketCommandReceived(const CString& command, const CString& params, const CString& requestId)
{
    try {
        CScadaCommandHandler* pHandler = GetScadaCommandHandler();
        if (!pHandler || !pHandler->IsInitialized()) {
            REPORT(_T("[SCADA命令] 命令处理器未初始化"), emLogLevelError);
            return 1; // 失败
        }

        REPORT(_T("[SCADA命令] 收到Socket命令: ") + command + _T(", RequestId: ") + requestId, emLogLevelNormal);

        // 获取当前Socket消息（从SimpleSocketInterface传递过来）
        // 这里我们需要重新构建完整的Socket消息处理
        // 暂时使用简化的命令格式
        CString commandJson;
        commandJson.Format(_T("{\"service_id\":\"CommandService\",\"command_name\":\"%s\",\"properties\":{\"reason\":\"Socket命令\"}}"),
                          command);

        // 处理命令
        CScadaCommandHandler::CommandResult result = pHandler->ProcessCommand(commandJson, requestId);

        // 发送响应给MQTT网关
        SimpleSocketInterface* pSocket = SimpleSocketInterface::GetInstance();
        if (pSocket && pSocket->IsConnected()) {
            // 构建标准SCADA响应消息
            Json::Value response;
            response["response_name"] = "COMMAND_RESPONSE";
            response["result_code"] = result.resultCode;

            // 构建properties字段
            Json::Value properties;
            if (result.resultCode == 0) {
                properties["result"] = "success";
            } else {
                properties["result"] = "failed";
                properties["error_message"] = result.resultMessage;
            }

            response["properties"] = properties;

            // 发送COMMAND_RESPONSE消息
            Json::FastWriter writer;
            std::string responseJson = writer.write(response);

            // 使用公有方法发送响应
            std::string requestIdStr = std::string(CT2A(requestId));
            if (SimpleSocketInterface::SendCommandResponse(requestIdStr, responseJson)) {
                REPORT(_T("[SCADA命令] 命令响应已发送"), emLogLevelNormal);
            } else {
                REPORT(_T("[SCADA命令] 命令响应发送失败"), emLogLevelError);
            }
        }

        return (result.resultCode == 0) ? 0 : 1;
    }
    catch (...) {
        REPORT(_T("[SCADA命令] Socket命令处理异常"), emLogLevelError);
        return 1; // 失败
    }
}
