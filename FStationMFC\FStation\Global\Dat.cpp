﻿#include "stdafx.h"
#include "Dat.h"

#include "Sys.h"

#include "BaseApi.h"
using namespace yzBase;

map<CString, DATACOLLECTION> CDat::m_mapDataCollection;
CRITICAL_SECTION CDat::m_csDataCollection;

CDat::CDat(CString strType)
{
	m_strType = strType;

	for (int i=0; i<2; i++)
	{
		m_nCheckResultForEveryDayNG[i] = 0;
		m_nCheckResultForEveryDayOK[i] = 0;
	}

	static bool bFlag = false;

	if (bFlag) {
		return;
	}

	CreateDataCollection("正常生产", "科瑞F站（二代）", "A00002", "A00002", "设备获取", true, 0);
	CreateDataCollection("运行暂停", "科瑞F站（二代）", "A00003", "A00003", "设备获取", true, 0);
	CreateDataCollection("设备故障", "科瑞F站（二代）", "A00004", "A00004", "设备获取", true, 0);
	CreateDataCollection("端口异常", "科瑞F站（二代）", "A00005", "A00005", "设备获取", true, "NULL");
	CreateDataCollection("待机状态", "科瑞F站（二代）", "A00006", "A00006", "设备获取", true, 0);
	CreateDataCollection("成功数", "科瑞F站（二代）", "A00008", "A00008", "设备获取", true, 0);
	CreateDataCollection("总测试数", "科瑞F站（二代）", "A00009", "A00009", "设备获取", true, 0);
	CreateDataCollection("机型程序名", "科瑞F站（二代）", "A00010", "A00010", "设备获取", true, "NULL");
	CreateDataCollection("主板SN号", "科瑞F站（二代）", "A00011", "A00011", "设备获取", true, "NULL");
	CreateDataCollection("当前测试结果", "科瑞F站（二代）", "A00012", "A00012", "设备获取", true, 0);
	CreateDataCollection("离线状态", "科瑞F站（二代）", "A00013", "Device.Status.Network", "SCADA计算", false, 0);
	CreateDataCollection("运行周期", "科瑞F站（二代）", "A00014", "A00014", "设备获取", true, "NULL");
	CreateDataCollection("累计产能", "科瑞F站（二代）", "A00015", "A00015", "设备获取", true, 0);
	CreateDataCollection("内存容量", "科瑞F站（二代）", "A00020", "RAM.Size", "Agent获取", false, 0);
	CreateDataCollection("磁盘容量", "科瑞F站（二代）", "A00021", "Disk.Size", "Agent获取", false, 0);
	CreateDataCollection("C盘容量", "科瑞F站（二代）", "A00022", "Disk.C.Size", "Agent获取", false, 0);
	CreateDataCollection("D盘容量", "科瑞F站（二代）", "A00023", "Disk.D.Size", "Agent获取", false, 0);
	CreateDataCollection("E盘容量", "科瑞F站（二代）", "A00024", "Disk.E.Size", "Agent获取", false, 0);
	CreateDataCollection("磁盘剩余容量", "科瑞F站（二代）", "A00025", "Disk.FreeSize", "Agent获取", false, 0);
	CreateDataCollection("C盘剩余容量", "科瑞F站（二代）", "A00026", "Disk.C.FreeSize", "Agent获取", false, 0);
	CreateDataCollection("D盘剩余容量", "科瑞F站（二代）", "A00027", "Disk.D.FreeSize", "Agent获取", false, 0);
	CreateDataCollection("E盘剩余容量", "科瑞F站（二代）", "A00028", "Disk.E.FreeSize", "Agent获取", false, 0);
	CreateDataCollection("CPU利用率", "科瑞F站（二代）", "A00029", "CPU.Usage", "Agent获取", false, 0);
	CreateDataCollection("内存利用率", "科瑞F站（二代）", "A00030", "RAM.Usage", "Agent获取", false, 0);
	CreateDataCollection("实际加工周期", "科瑞F站（二代）", "A00031", "A00031", "SCADA计算", false, 0);
	CreateDataCollection("正常生产时间", "科瑞F站（二代）", "A00032", "A00032", "SCADA计算", false, 0);
	CreateDataCollection("运行暂停时间", "科瑞F站（二代）", "A00033", "A00033", "SCADA计算", false, 0);
	CreateDataCollection("设备故障时间", "科瑞F站（二代）", "A00034", "A00034", "SCADA计算", false, 0);
	CreateDataCollection("待机时间", "科瑞F站（二代）", "A00035", "A00035", "SCADA计算", false, 0);
	CreateDataCollection("离线状态时间", "科瑞F站（二代）", "A00036", "A00036", "SCADA计算", false, 0);
	CreateDataCollection("生产转产时间", "科瑞F站（二代）", "A00037", "A00037", "MES获取", false, 0);
	CreateDataCollection("时间稼动率", "科瑞F站（二代）", "A00038", "A00038", "SCADA计算", false, 0);
	CreateDataCollection("性能稼动率", "科瑞F站（二代）", "A00039", "A00039", "SCADA计算", false, 0);
	CreateDataCollection("良率", "科瑞F站（二代）", "A00040", "A00040", "SCADA计算", false, 0);
	CreateDataCollection("OEE", "科瑞F站（二代）", "A00041", "A00041", "SCADA计算", false, 0);
	CreateDataCollection("MTBF", "科瑞F站（二代）", "A00042", "A00042", "SCADA计算", false, 0);
	CreateDataCollection("正常生产时间-MTBF", "科瑞F站（二代）", "A00043", "A00043", "SCADA计算", false, 0);
	CreateDataCollection("故障次数-MTBF", "科瑞F站（二代）", "A00044", "A00044", "SCADA计算", false, 0);
	CreateDataCollection("MTTR", "科瑞F站（二代）", "A00045", "A00045", "SCADA计算", false, 0);
	CreateDataCollection("故障时间-MTTR", "科瑞F站（二代）", "A00046", "A00046", "SCADA计算", false, 0);
	CreateDataCollection("故障次数-MTTR", "科瑞F站（二代）", "A00047", "A00047", "SCADA计算", false, 0);
	CreateDataCollection("机器人连接状态", "科瑞F站（二代）", "B40001", "B40001", "设备获取", true, 0);
	CreateDataCollection("气源状态", "科瑞F站（二代）", "B40002", "B40002", "设备获取", true, 0);
	CreateDataCollection("上相机状态", "科瑞F站（二代）", "B40003", "B40003", "设备获取", true, 0);
	CreateDataCollection("下相机状态", "科瑞F站（二代）", "B40004", "B40004", "设备获取", true, 0);
	CreateDataCollection("顶部相机状态", "科瑞F站（二代）", "B40005", "B40005", "设备获取", true, 0);
	CreateDataCollection("设备故障信息", "科瑞F站（二代）", "B40006", "B40006", "设备获取", true, "NULL");
	CreateDataCollection("设备故障开始/结束状态", "科瑞F站（二代）", "B40007", "B40007", "设备获取", true, 0);
	CreateDataCollection("1轨测高值1", "科瑞F站（二代）", "B40008", "B40008", "设备获取", true, 0.0);
	CreateDataCollection("1轨测高值2", "科瑞F站（二代）", "B40009", "B40009", "设备获取", true, 0.0);
	CreateDataCollection("1轨测高值3", "科瑞F站（二代）", "B40010", "B40010", "设备获取", true, 0.0);
	CreateDataCollection("1轨测高值4", "科瑞F站（二代）", "B40011", "B40011", "设备获取", true, 0.0);
	CreateDataCollection("2轨测高值1", "科瑞F站（二代）", "B40012", "B40012", "设备获取", true, 0.0);
	CreateDataCollection("2轨测高值2", "科瑞F站（二代）", "B40013", "B40013", "设备获取", true, 0.0);
	CreateDataCollection("2轨测高值3", "科瑞F站（二代）", "B40014", "B40014", "设备获取", true, 0.0);
	CreateDataCollection("2轨测高值4", "科瑞F站（二代）", "B40015", "B40015", "设备获取", true, 0.0);
	CreateDataCollection("测高阈值", "科瑞F站（二代）", "B40016", "B40016", "设备获取", true, 0.0);
	CreateDataCollection("治具供料器NG数", "科瑞F站（二代）", "B40017", "B40017", "设备获取", true, 0);
	CreateDataCollection("Try盘供料器NG数", "科瑞F站（二代）", "B40018", "B40018", "设备获取", true, 0);
	CreateDataCollection("上相机拍照失败数", "科瑞F站（二代）", "B40019", "B40019", "设备获取", true, 0);
	CreateDataCollection("下相机拍照失败数", "科瑞F站（二代）", "B40020", "B40020", "设备获取", true, 0);
	CreateDataCollection("顶部相机拍照失败数", "科瑞F站（二代）", "B40021", "B40021", "设备获取", true, 0);
	CreateDataCollection("1号吸嘴取板失败数", "科瑞F站（二代）", "B40022", "B40022", "设备获取", true, 0);
	CreateDataCollection("2号吸嘴取板失败数", "科瑞F站（二代）", "B40023", "B40023", "设备获取", true, 0);
	CreateDataCollection("3号吸嘴取板失败数", "科瑞F站（二代）", "B40024", "B40024", "设备获取", true, 0);
	CreateDataCollection("4号吸嘴取板失败数", "科瑞F站（二代）", "B40025", "B40025", "设备获取", true, 0);

	CreateDataCollection("主板SN号", "科瑞F站（二代）", "C00001", "C00001", "设备获取", true, "NULL");
	//CreateDataCollection("设备资产编码", "科瑞F站（二代）", "C00002", "C00002", "设备获取", true, "NULL");
	CreateDataCollection("轨道号", "科瑞F站（二代）", "C00003", "C00003", "设备获取", true, 1);
	CreateDataCollection("面别", "科瑞F站（二代）", "C00004", "C00004", "设备获取", true, "NULL");
	CreateDataCollection("程序名", "科瑞F站（二代）", "C00005", "C00005", "设备获取", true, "NULL");
	CreateDataCollection("程序路径", "科瑞F站（二代）", "C00006", "C00006", "设备获取", true, "NULL");
	CreateDataCollection("实际加工周期", "科瑞F站（二代）", "C00007", "C00007", "设备获取", true, 0.0);
	CreateDataCollection("等前时间", "科瑞F站（二代）", "C00008", "C00008", "设备获取", true, 0.0);
	CreateDataCollection("等后时间", "科瑞F站（二代）", "C00009", "C00009", "设备获取", true, 0.0);
	CreateDataCollection("故障代码", "科瑞F站（二代）", "C00010", "C00010", "设备获取", true, "NULL");
	CreateDataCollection("故障信息", "科瑞F站（二代）", "C00011", "C00011", "设备获取", true, "NULL");
	CreateDataCollection("生产开始时间", "科瑞F站（二代）", "C00012", "C00012", "设备获取", true, "NULL");
	CreateDataCollection("生产结束时间", "科瑞F站（二代）", "C00013", "C00013", "设备获取", true, "NULL");
	CreateDataCollection("生产总时间", "科瑞F站（二代）", "C00014", "C00014", "设备获取", true, 0.0);

	InitializeCriticalSection(&m_csDataCollection);

	bFlag = true;
}

CDat::~CDat()
{

}

CString CDat::GetDataDir()
{
	CString strPath = CString(GetModulePath().c_str());

	CString strDate;
	CTime t = CTime::GetCurrentTime();
	strDate.Format("\\%s\\%04d-%02d-%02d\\", m_strType, t.GetYear(), t.GetMonth(), t.GetDay());

	CString strDatePath = strPath + strDate;

	return strDatePath;
}

void CDat::GenDateDir()
{
	CString strDatePath = GetDataDir();

	CreatePath(strDatePath.GetBuffer());
	strDatePath.ReleaseBuffer();
}

void CDat::ClearOutDateDir(int nOutDay)
{
	CString strDate;
	CTime t = CTime::GetCurrentTime();

	CTime tToday(t.GetYear(), t.GetMonth(), t.GetDay(), 0, 0, 0);

	int nRecordDay = 0, nRecordMonth = 0, nRecordYear = 0;

	CString strPath = CString(GetModulePath().c_str());
	CString strFile;
	strFile.Format("%s\\%s\\", strPath, m_strType);

	CString strFileName, strPathName;

	CFileFind ff;
	BOOL bFlag = ff.FindFile(strFile + "*.*");
	while (bFlag) {
		bFlag = ff.FindNextFile();

		if (ff.IsDirectory()) {
			strFileName = ff.GetFileName();
			if (strFileName.GetLength() < 10) {
				continue;
			}

			nRecordDay = atoi(strFileName.Mid(8, 2));
			nRecordMonth = atoi(strFileName.Mid(5, 2));
			nRecordYear = atoi(strFileName.Mid(0, 4));

			CTime tRecord(nRecordYear, nRecordMonth,nRecordDay, 0, 0, 0);
			CTimeSpan tSpan = tToday - tRecord;
			ULONGLONG nDays = tSpan.GetDays();

			if (nDays > nOutDay) {
				strPathName = ff.GetFilePath();
				DeleteDir(strPathName.GetBuffer());
				strPathName.ReleaseBuffer();
			}
		}
	}
}

bool CDat::DeleteDir(CString dirName)
{
	CFileFind tempFind;
	BOOL IsFinded = (BOOL)tempFind.FindFile(dirName + "\\*.*");
	while(IsFinded)
	{
		IsFinded = (BOOL)tempFind.FindNextFile();
		if(!tempFind.IsDots())
		{
			CString foundFileName;
			foundFileName = tempFind.GetFileName();
			if(tempFind.IsDirectory())
			{
				DeleteDir(dirName + "\\" + foundFileName);
			}
			else
			{
				CString tempFileName;
				tempFileName.Format("%s\\%s",dirName,foundFileName);
				DeleteFile(tempFileName);
			}
		}
	}

	tempFind.Close();

	if(!RemoveDirectory(dirName))
	{
		return false;
	}

	return true;
} 

std::map<int, MCHECKRESULT> CDat::ReadCheckData(unsigned int nYear, unsigned int nMonth, unsigned int nDay)
{
	map<int, MCHECKRESULT> mCheckResult;

	CString strDate;
	strDate.Format("%04d%02d%02d", nYear, nMonth, nDay);

	unsigned int nDate = 0;
	nDate	= atoi(strDate);

	CTime t = CTime::GetCurrentTime();

	CString str;
	str.Format("%04d%02d%02d", t.GetYear(), t.GetMonth(), t.GetDay());

	unsigned int nToday = 0;
	nToday	= atoi(str);

	if (nDate > nToday) {
		return mCheckResult;
	}

	CString sFile;
	for (int i=0; i<2; i++)
	{
		sFile.Format("D:\\LOG_CHECK_RESULT\\%04d-%02d-%02d\\%02d.csv", nYear, nMonth, nDay, i + 1);

		CFileFind ff;
		if (!ff.FindFile(sFile)) {
			continue;
		}

		CStdioFile sf;
		sf.Open(sFile, CFile::modeCreate | CFile::modeNoTruncate | CFile::modeReadWrite);
		sf.SeekToBegin();

		int nIndex = 0;

		for (;;)
		{
			sf.ReadString(str);
			if (str.IsEmpty()) {
				break;
			}

			CString sName;
			CHECKRESULT stCheckResult;

			nIndex = str.Find(",");
			if (nIndex < 0) {
				continue;
			}

			stCheckResult.sTime = str.Left(nIndex);

			str = str.Mid(nIndex + 1);

			nIndex = str.Find(",");
			if (nIndex < 0) {
				continue;
			}

			stCheckResult.sModel = str.Left(nIndex);

			str = str.Mid(nIndex + 1);

			nIndex = str.Find(",");
			if (nIndex < 0) {
				continue;
			}

			stCheckResult.sCode = str.Left(nIndex);

			str = str.Mid(nIndex + 1);

			nIndex = str.Find(",");
			if (nIndex < 0) {
				continue;
			}

			sName = str.Left(nIndex);

			str = str.Mid(nIndex + 1);

			nIndex = str.Find(",");
			if (nIndex < 0) {
				continue;
			}

			stCheckResult.sResult = str.Left(nIndex);

			str = str.Mid(nIndex + 1);

			nIndex = str.Find(",");
			if (nIndex < 0) {
				continue;
			}

			stCheckResult.nArea[0] = atof(str.Left(nIndex));

			str = str.Mid(nIndex + 1);

			nIndex = str.Find(",");
			if (nIndex < 0) {
				continue;
			}

			stCheckResult.nAreaScore[0] = atof(str.Left(nIndex));

			str = str.Mid(nIndex + 1);

			nIndex = str.Find(",");
			if (nIndex < 0) {
				continue;
			}

			stCheckResult.nOffX[0] = atof(str.Left(nIndex));

			str = str.Mid(nIndex + 1);

			nIndex = str.Find(",");
			if (nIndex < 0) {
				continue;
			}

			stCheckResult.nOffY[0] = atof(str.Left(nIndex));

			str = str.Mid(nIndex + 1);

			nIndex = str.Find(",");
			if (nIndex < 0) {
				continue;
			}

			stCheckResult.nArea[1] = atof(str.Left(nIndex));

			str = str.Mid(nIndex + 1);

			nIndex = str.Find(",");
			if (nIndex < 0) {
				continue;
			}

			stCheckResult.nAreaScore[1] = atof(str.Left(nIndex));

			str = str.Mid(nIndex + 1);

			nIndex = str.Find(",");
			if (nIndex < 0) {
				continue;
			}

			stCheckResult.nOffX[1] = atof(str.Left(nIndex));

			str = str.Mid(nIndex + 1);

			stCheckResult.nOffY[1] = atof(str);

			mCheckResult[i][sName].push_back(stCheckResult);
		}
	}

	return mCheckResult;
}

void CDat::UpdateSum(bool bOkSum)
{
	GenDateDir();

	CString strDatPath = GetDataDir();
	strDatPath += "\\Sum.csv";

	CString str;

	EnterCriticalSection(&m_csDataCollection);

	CStdioFile sf;
	sf.Open(strDatPath, CFile::modeCreate | CFile::modeNoTruncate | CFile::modeReadWrite);
	sf.SeekToBegin();
	sf.ReadString(str);
	sf.ReadString(str);
	sf.Flush();
	sf.Close();

	int nOk = 0, nNg = 0, nIndex = 0;
	if (!str.IsEmpty()) {
		nIndex = str.Find(", ");
		if (nIndex > 0) {
			nOk = atoi(str.Left(nIndex));
			nNg = atoi(str.Right(str.GetLength() - nIndex - 1));
		}
	}

	if (bOkSum) {
		nOk++;
	}
	else {
		nNg++;
	}

	str.Format("%d,%d\n", nOk, nNg);

	sf.Open(strDatPath, CFile::modeCreate | CFile::modeReadWrite);
	sf.SeekToBegin();
	sf.WriteString("OK,NG\n");
	sf.WriteString(str);
	sf.Flush();
	sf.Close();
	
	LeaveCriticalSection(&m_csDataCollection);
}

void CDat::UpdateSpecialSum(bool bBad)
{
	GenDateDir();

	CString strDatPath = GetDataDir();
	strDatPath += "\\LogSpecial.ini";

	CTime t = CTime::GetCurrentTime();

	CString sKey;
	sKey.Format("%02d", t.GetHour());

	if (bBad) {
		m_nCheckResultForEveryDayNG[t.GetHour()]++;
		WriteIni(sKey.GetBuffer(), "NG", m_nCheckResultForEveryDayNG[t.GetHour()], strDatPath.GetBuffer());
	}
	else {
		m_nCheckResultForEveryDayOK[t.GetHour()]++;
		WriteIni(sKey.GetBuffer(), "OK", m_nCheckResultForEveryDayOK[t.GetHour()], strDatPath.GetBuffer());
	}
}

void CDat::UpdateLog(CString strLog)
{
	GenDateDir();

	CString strDatPath = GetDataDir();
	strDatPath += "\\Log.txt";

	EnterCriticalSection(&m_csDataCollection);

	CStdioFile sf;
	sf.Open(strDatPath, CFile::modeCreate | CFile::modeNoTruncate | CFile::modeReadWrite);
	sf.SeekToEnd();
	sf.WriteString(strLog);
	sf.Flush();
	sf.Close();

	LeaveCriticalSection(&m_csDataCollection);
}

void CDat::UpdateErrorLog(CString strErr)
{
	GenDateDir();

	CString strDatPath = GetDataDir();
	strDatPath += "\\Error.txt";

	SYSTEMTIME t = { 0 };
	GetLocalTime(&t);

	CString strTime;
	strTime.Format("%02d:%02d:%02d:%03d", t.wHour, t.wMinute, t.wSecond, t.wMilliseconds);

	strErr.Replace("\n", "");

	CString strLogToRecord;
	strLogToRecord.Format("%s - %s\n", strTime, strErr);

	EnterCriticalSection(&m_csDataCollection);

	CStdioFile sf;
	sf.Open(strDatPath, CFile::modeCreate | CFile::modeNoTruncate | CFile::modeReadWrite);
	sf.SeekToEnd();
	sf.WriteString(strLogToRecord);
	sf.Flush();
	sf.Close();

	LeaveCriticalSection(&m_csDataCollection);
}

void CDat::UpdateParameterLog(CString strLog)
{
	GenDateDir();

	CString strDatPath = GetDataDir();
	strDatPath += "\\LogParam.txt";

	EnterCriticalSection(&m_csDataCollection);

	CStdioFile sf;
	sf.Open(strDatPath, CFile::modeCreate | CFile::modeNoTruncate | CFile::modeReadWrite);
	sf.SeekToEnd();
	sf.WriteString(strLog);
	sf.Flush();
	sf.Close();

	LeaveCriticalSection(&m_csDataCollection);
}

void CDat::SaveImage(CCvImage* pImage, CString sDirName, CString sInfo, double nZoomRate, bool bInit)
{
	GenDateDir();

	CString strDatPath = GetDataDir();

	CString sImgPath = strDatPath + sDirName;

	CreatePath(sImgPath.GetBuffer());
	sImgPath.ReleaseBuffer();

	CTime t = CTime::GetCurrentTime();

	CString sName;
	if (bInit && VAR_MACHINE_S("图片存储格式") == "bmp") {
		sName.Format("\\%02d-%02d-%02d_%s.bmp", t.GetHour(), t.GetMinute(), t.GetSecond(), sInfo);
	}
	else {
		sName.Format("\\%02d-%02d-%02d_%s.jpg", t.GetHour(), t.GetMinute(), t.GetSecond(), sInfo);
	}

	pImage->SaveImage(sImgPath + sName, nZoomRate, bInit);
}

void CDat::CreateDataCollection(CString sName, CString sMachineType, CString sDataCode, CString sSectionCode, CString sDataSource, bool bMachineExisted, int nInitValue)
{
	DATACOLLECTION stDataCollection;
	stDataCollection.sMachineType = sMachineType;
	stDataCollection.sDataCode = sDataCode;
	stDataCollection.sSectionCode = sSectionCode;
	stDataCollection.sDataSource = sDataSource;
	stDataCollection.bMachineExisted = bMachineExisted;
	stDataCollection.pDat = new CData(nInitValue, -999999, 999999);

	m_mapDataCollection[sName] = stDataCollection;
}

void CDat::CreateDataCollection(CString sName, CString sMachineType, CString sDataCode, CString sSectionCode, CString sDataSource, bool bMachineExisted, double nInitValue)
{
	DATACOLLECTION stDataCollection;
	stDataCollection.sMachineType = sMachineType;
	stDataCollection.sDataCode = sDataCode;
	stDataCollection.sSectionCode = sSectionCode;
	stDataCollection.sDataSource = sDataSource;
	stDataCollection.bMachineExisted = bMachineExisted;
	stDataCollection.pDat = new CData(nInitValue, -999999.999, 999999.999);

	m_mapDataCollection[sName] = stDataCollection;
}

void CDat::CreateDataCollection(CString sName, CString sMachineType, CString sDataCode, CString sSectionCode, CString sDataSource, bool bMachineExisted, CString sInitValue)
{
	DATACOLLECTION stDataCollection;
	stDataCollection.sMachineType = sMachineType;
	stDataCollection.sDataCode = sDataCode;
	stDataCollection.sSectionCode = sSectionCode;
	stDataCollection.sDataSource = sDataSource;
	stDataCollection.bMachineExisted = bMachineExisted;
	stDataCollection.pDat = new CData(sInitValue);

	m_mapDataCollection[sName] = stDataCollection;
}

void CDat::UpdateValue(CString sName, int nNewValue)
{
	CString strDatePath;
	CTime t = CTime::GetCurrentTime();
	strDatePath.Format("D:\\LOG_NEW\\%04d-%02d-%02d\\", t.GetYear(), t.GetMonth(), t.GetDay());

	CreatePath(strDatePath.GetBuffer());
	strDatePath.ReleaseBuffer();
	
	CString sValue;
	sValue.Format("%d", nNewValue);

	if (m_mapDataCollection[sName].pDat->UpdateValue(sValue).IsEmpty()) {
		return;
	}

// 	CString strLog;
// 	strLog.Format("%s,%s,%d,%04d-%02d-%02d %02d:%02d:%02d\n", m_mapDataCollection[sName].sDataCode, sName, nNewValue, t.GetYear(), t.GetMonth(), t.GetDay(), t.GetHour(), t.GetMinute(), t.GetSecond());
// 
// 	CString strFile;
// 	strFile.Format("%s%04d-%02d-%02d.txt", strDatePath, t.GetYear(), t.GetMonth(), t.GetDay());
// 
// 	EnterCriticalSection(&m_csDataCollection);
// 
// 	CStdioFile sf;
// 	sf.Open(strFile, CFile::modeCreate | CFile::modeNoTruncate | CFile::modeReadWrite | CFile::shareDenyNone);
// 	sf.SeekToEnd();
// 	sf.WriteString(strLog);
// 	sf.Flush();
// 	sf.Close();
// 
// 	LeaveCriticalSection(&m_csDataCollection);
}

void CDat::IncreaseValue(CString sName)
{
	CString strDatePath;
	CTime t = CTime::GetCurrentTime();
	strDatePath.Format("D:\\LOG_NEW\\%04d-%02d-%02d\\", t.GetYear(), t.GetMonth(), t.GetDay());

	CreatePath(strDatePath.GetBuffer());
	strDatePath.ReleaseBuffer();

	CString sValue;
	sValue.Format("%d", m_mapDataCollection[sName].pDat->I() + 1);

	if (m_mapDataCollection[sName].pDat->UpdateValue(sValue).IsEmpty()) {
		return;
	}

	CString strLog;
	strLog.Format("%s,%s,%g,%04d-%02d-%02d %02d:%02d:%02d,0,0\n", m_mapDataCollection[sName].sDataCode, sName, m_mapDataCollection[sName].pDat->I(), t.GetYear(), t.GetMonth(), t.GetDay(), t.GetHour(), t.GetMinute(), t.GetSecond());

	CString strFile;
	strFile.Format("%s%04d-%02d-%02d.txt", strDatePath, t.GetYear(), t.GetMonth(), t.GetDay());

	EnterCriticalSection(&m_csDataCollection);

	CStdioFile sf;
	sf.Open(strFile, CFile::modeCreate | CFile::modeNoTruncate | CFile::modeReadWrite | CFile::shareDenyNone);
	sf.SeekToEnd();
	sf.WriteString(strLog);
	sf.WriteString("\n");
	sf.Flush();
	sf.Close();

	LeaveCriticalSection(&m_csDataCollection);
}


void CDat::UpdateValue(CString sName, double nNewValue)
{
	CString strDatePath;
	CTime t = CTime::GetCurrentTime();
	strDatePath.Format("D:\\LOG_NEW\\%04d-%02d-%02d\\", t.GetYear(), t.GetMonth(), t.GetDay());

	CreatePath(strDatePath.GetBuffer());
	strDatePath.ReleaseBuffer();

	CString sValue;
	sValue.Format("%g", nNewValue);

	m_mapDataCollection[sName].pDat->UpdateValue(sValue);

// 	CString strLog;
// 	strLog.Format("%s,%s,%g,%04d-%02d-%02d %02d:%02d:%02d\n", m_mapDataCollection[sName].sDataCode, sName, nNewValue, t.GetYear(), t.GetMonth(), t.GetDay(), t.GetHour(), t.GetMinute(), t.GetSecond());
// 
// 	CString strFile;
// 	strFile.Format("%s%04d-%02d-%02d.txt", strDatePath, t.GetYear(), t.GetMonth(), t.GetDay());
// 
// 	EnterCriticalSection(&m_csDataCollection);
// 
// 	CStdioFile sf;
// 	sf.Open(strFile, CFile::modeCreate | CFile::modeNoTruncate | CFile::modeReadWrite | CFile::shareDenyNone);
// 	sf.SeekToEnd();
// 	sf.WriteString(strLog);
// 	sf.Flush();
// 	sf.Close();
// 
// 	LeaveCriticalSection(&m_csDataCollection);
}

void CDat::UpdateValue(CString sName, CString sValue)
{
	CString strDatePath;
	CTime t = CTime::GetCurrentTime();
	strDatePath.Format("D:\\LOG_NEW\\%04d-%02d-%02d\\", t.GetYear(), t.GetMonth(), t.GetDay());

	CreatePath(strDatePath.GetBuffer());
	strDatePath.ReleaseBuffer();

	if (m_mapDataCollection[sName].pDat->UpdateValue(sValue).IsEmpty()) {
		return;
	}

// 	sValue.Replace(",", "，");
// 
// 	CString strLog;
// 	strLog.Format("%s,%s,%s,%04d-%02d-%02d %02d:%02d:%02d\n", m_mapDataCollection[sName].sDataCode, sName, sValue, t.GetYear(), t.GetMonth(), t.GetDay(), t.GetHour(), t.GetMinute(), t.GetSecond());
// 
// 	CString strFile;
// 	strFile.Format("%s%04d-%02d-%02d.txt", strDatePath, t.GetYear(), t.GetMonth(), t.GetDay());
// 
// 	EnterCriticalSection(&m_csDataCollection);
// 
// 	CStdioFile sf;
// 	sf.Open(strFile, CFile::modeCreate | CFile::modeNoTruncate | CFile::modeReadWrite | CFile::shareDenyNone);
// 	sf.SeekToEnd();
// 	sf.WriteString(strLog);
// 	sf.Flush();
// 	sf.Close();
// 
// 	LeaveCriticalSection(&m_csDataCollection);
}

void CDat::Update()
{
	CString strDatePath;
	CTime t = CTime::GetCurrentTime();
	strDatePath.Format("D:\\LOG_NEW\\%04d-%02d-%02d\\", t.GetYear(), t.GetMonth(), t.GetDay());

	CreatePath(strDatePath.GetBuffer());
	strDatePath.ReleaseBuffer();

	CString strFile;
	strFile.Format("%s%04d-%02d-%02d.txt", strDatePath, t.GetYear(), t.GetMonth(), t.GetDay());

	EnterCriticalSection(&m_csDataCollection);

	CStdioFile sf;
	sf.Open(strFile, CFile::modeCreate | CFile::modeNoTruncate | CFile::modeReadWrite | CFile::shareDenyNone);
	sf.SeekToEnd();

	CString sToday;
	sToday.Format("%04d%02d%02d", t.GetYear(), t.GetMonth(), t.GetDay());

	if (sToday != CSys::m_sToday) {
		CSys::m_nTodayOK = 0;
		CSys::m_nTodayNG = 0;
		CSys::m_sToday = sToday;

		CString strPath = CString(GetModulePath().c_str());
		CString strSysPath = strPath + "\\Sys\\Sys.ini";
		WriteIni("Machine", "TodayOK", CSys::m_nTodayOK, strSysPath.GetBuffer());
		WriteIni("Machine", "TodayNG", CSys::m_nTodayNG, strSysPath.GetBuffer());
		WriteIni("Machine", "Today", sToday.GetBuffer(), strSysPath.GetBuffer());
	}

	CString strLog;

	map<CString, DATACOLLECTION>::iterator it = CDat::m_mapDataCollection.begin();
	for (; it != CDat::m_mapDataCollection.end(); it++)
	{
		if (!it->second.bMachineExisted) {
			continue;
		}

		strLog.Format("%s,%s,%s,%04d-%02d-%02d %02d:%02d:%02d,0,0\n", it->second.sDataCode, it->first, it->second.pDat->Value(), t.GetYear(), t.GetMonth(), t.GetDay(), t.GetHour(), t.GetMinute(), t.GetSecond());
		sf.WriteString(strLog);
		sf.SeekToEnd();
	}

	sf.Flush();
	sf.Close();

	LeaveCriticalSection(&m_csDataCollection);
}

void CDat::UpdateValueManualOperation( int nStatus )
{
	CString strDatePath;
	CTime t = CTime::GetCurrentTime();
	strDatePath.Format("D:\\LOG_NEW\\%04d-%02d-%02d\\", t.GetYear(), t.GetMonth(), t.GetDay());

	CreatePath(strDatePath.GetBuffer());
	strDatePath.ReleaseBuffer();

	CString strFile;
	strFile.Format("%s%04d-%02d-%02d.txt", strDatePath, t.GetYear(), t.GetMonth(), t.GetDay());

	EnterCriticalSection(&m_csDataCollection);

	CStdioFile sf;
	sf.Open(strFile, CFile::modeCreate | CFile::modeNoTruncate | CFile::modeReadWrite | CFile::shareDenyNone);
	sf.SeekToEnd();

	CString sToday;
	sToday.Format("%04d%02d%02d", t.GetYear(), t.GetMonth(), t.GetDay());

	if (sToday != CSys::m_sToday) {
		CSys::m_nTodayOK = 0;
		CSys::m_nTodayNG = 0;
		CSys::m_sToday = sToday;

		CString strPath = CString(GetModulePath().c_str());
		CString strSysPath = strPath + "\\Sys\\Sys.ini";
		WriteIni("Machine", "TodayOK", CSys::m_nTodayOK, strSysPath.GetBuffer());
		WriteIni("Machine", "TodayNG", CSys::m_nTodayNG, strSysPath.GetBuffer());
		WriteIni("Machine", "Today", sToday.GetBuffer(), strSysPath.GetBuffer());
	}

	CDat::UpdateValue("正常生产", 0);
	CDat::UpdateValue("设备故障", 0);
	CDat::UpdateValue("运行暂停", 0);
	CDat::UpdateValue("待机状态", 0);

	switch (nStatus)
	{
	case 0:
		if (g_nWarnTimes <= 0) {
			CDat::UpdateValue("正常生产", 1);
		}
		else {
			CDat::UpdateValue("设备故障", 1);
		}
		break;
	case 1:
		if (g_nWarnTimes <= 0) {
			CDat::UpdateValue("运行暂停", 1);
		}
		else {
			CDat::UpdateValue("设备故障", 1);
		}
		break;
	case 2:
		CDat::UpdateValue("待机状态", 1);
		break;
	default:break;
	}

	CDat::m_mapDataCollection["机型程序名"].pDat->UpdateValue(CSys::m_strPro.c_str());
	CDat::m_mapDataCollection["气源状态"].pDat->UpdateValue("1");
	CDat::m_mapDataCollection["上相机状态"].pDat->UpdateValue("1");
	CDat::m_mapDataCollection["下相机状态"].pDat->UpdateValue("1");
	CDat::m_mapDataCollection["顶部相机状态"].pDat->UpdateValue("1");

	CString sItem[] = {
		"正常生产",
		"运行暂停",
		"设备故障",
		"待机状态",
		"机型程序名",
		"气源状态",
		"上相机状态",
		"下相机状态",
		"顶部相机状态"
	};

	CString strLog;

	for (int i=0; i<9; i++)
	{
		strLog.Format("%s,%s,%s,%04d-%02d-%02d %02d:%02d:%02d,0,0\n", CDat::m_mapDataCollection[sItem[i]].sDataCode, sItem[i], CDat::m_mapDataCollection[sItem[i]].pDat->Value(), t.GetYear(), t.GetMonth(), t.GetDay(), t.GetHour(), t.GetMinute(), t.GetSecond());
		sf.WriteString(strLog);
		sf.SeekToEnd();
	}

	sf.WriteString("\n");

	sf.Flush();
	sf.Close();

	LeaveCriticalSection(&m_csDataCollection);
}

void CDat::UpdateValueEveryHour()
{
	CString strDatePath;
	CTime t = CTime::GetCurrentTime();
	strDatePath.Format("D:\\LOG_NEW\\%04d-%02d-%02d\\", t.GetYear(), t.GetMonth(), t.GetDay());

	CreatePath(strDatePath.GetBuffer());
	strDatePath.ReleaseBuffer();

	CString strFile;
	strFile.Format("%s%04d-%02d-%02d.txt", strDatePath, t.GetYear(), t.GetMonth(), t.GetDay());

	EnterCriticalSection(&m_csDataCollection);

	CStdioFile sf;
	sf.Open(strFile, CFile::modeCreate | CFile::modeNoTruncate | CFile::modeReadWrite | CFile::shareDenyNone);
	sf.SeekToEnd();

	CString sToday;
	sToday.Format("%04d%02d%02d", t.GetYear(), t.GetMonth(), t.GetDay());

	if (sToday != CSys::m_sToday) {
		CSys::m_nTodayOK = 0;
		CSys::m_nTodayNG = 0;
		CSys::m_sToday = sToday;

		CString strPath = CString(GetModulePath().c_str());
		CString strSysPath = strPath + "\\Sys\\Sys.ini";
		WriteIni("Machine", "TodayOK", CSys::m_nTodayOK, strSysPath.GetBuffer());
		WriteIni("Machine", "TodayNG", CSys::m_nTodayNG, strSysPath.GetBuffer());
		WriteIni("Machine", "Today", sToday.GetBuffer(), strSysPath.GetBuffer());
	}

	CDat::UpdateValue("正常生产", 0);
	CDat::UpdateValue("设备故障", 0);
	CDat::UpdateValue("运行暂停", 0);
	CDat::UpdateValue("待机状态", 0);

	if (CSys::m_emRunStatus == emRunStatusRunning) {
		if (g_bWaitFlag) {
			CDat::UpdateValue("待机状态", 1);
		}
		else {
			CDat::UpdateValue("正常生产", 1);
		}
	}
	else if (CSys::m_emRunStatus == emRunStatusSuspending) {
		if (g_nWarnTimes > 0) {
			CDat::UpdateValue("设备故障", 1);
		}
		else {
			CDat::UpdateValue("运行暂停", 1);
		}
	}
	else {
		CDat::UpdateValue("待机状态", 1);
	}


	CDat::m_mapDataCollection["机型程序名"].pDat->UpdateValue(CSys::m_strPro.c_str());
	CDat::m_mapDataCollection["气源状态"].pDat->UpdateValue("1");
	CDat::m_mapDataCollection["上相机状态"].pDat->UpdateValue("1");
	CDat::m_mapDataCollection["下相机状态"].pDat->UpdateValue("1");
	CDat::m_mapDataCollection["顶部相机状态"].pDat->UpdateValue("1");

	CString sItem[] = {
		"正常生产",
		"运行暂停",
		"设备故障",
		"待机状态",
		"机型程序名",
		"气源状态",
		"上相机状态",
		"下相机状态",
		"顶部相机状态"
	};

	CString strLog;

	for (int i=0; i<9; i++)
	{
		strLog.Format("%s,%s,%s,%04d-%02d-%02d %02d:%02d:%02d,0,0\n", CDat::m_mapDataCollection[sItem[i]].sDataCode, sItem[i], CDat::m_mapDataCollection[sItem[i]].pDat->Value(), t.GetYear(), t.GetMonth(), t.GetDay(), t.GetHour(), t.GetMinute(), t.GetSecond());
		sf.WriteString(strLog);
		sf.SeekToEnd();
	}

	sf.WriteString("\n");

	sf.Flush();
	sf.Close();

	LeaveCriticalSection(&m_csDataCollection);
}

void CDat::UpdateValueEveryPiece()
{
	CString strDatePath;
	CTime t = CTime::GetCurrentTime();
	strDatePath.Format("D:\\LOG_NEW\\%04d-%02d-%02d\\", t.GetYear(), t.GetMonth(), t.GetDay());

	CreatePath(strDatePath.GetBuffer());
	strDatePath.ReleaseBuffer();

	CString strFile;
	strFile.Format("%s%04d-%02d-%02d.txt", strDatePath, t.GetYear(), t.GetMonth(), t.GetDay());

	EnterCriticalSection(&m_csDataCollection);

	CStdioFile sf;
	sf.Open(strFile, CFile::modeCreate | CFile::modeNoTruncate | CFile::modeReadWrite | CFile::shareDenyNone);
	sf.SeekToEnd();

	CString sItem[] = {
		"运行周期",
		"累计产能",
		"测高阈值",
		"1轨测高值1",
		"1轨测高值2",
		"1轨测高值3",
		"1轨测高值4",
		"2轨测高值1",
		"2轨测高值2",
		"2轨测高值3",
		"2轨测高值4"
	};

	CString strLog;

	for (int i=0; i<11; i++)
	{
		if (i >= 7 && i <= 10) {
			strLog.Format("%s,%s,%s,%04d-%02d-%02d %02d:%02d:%02d,1,0\n", CDat::m_mapDataCollection[sItem[i]].sDataCode, sItem[i], CDat::m_mapDataCollection[sItem[i]].pDat->Value(), t.GetYear(), t.GetMonth(), t.GetDay(), t.GetHour(), t.GetMinute(), t.GetSecond());
		}
		else {
			strLog.Format("%s,%s,%s,%04d-%02d-%02d %02d:%02d:%02d,0,0\n", CDat::m_mapDataCollection[sItem[i]].sDataCode, sItem[i], CDat::m_mapDataCollection[sItem[i]].pDat->Value(), t.GetYear(), t.GetMonth(), t.GetDay(), t.GetHour(), t.GetMinute(), t.GetSecond());
		}

		sf.WriteString(strLog);
		sf.SeekToEnd();
	}

	sf.WriteString("\n");

	sf.Flush();
	sf.Close();

	LeaveCriticalSection(&m_csDataCollection);
}

void CDat::UpdateValueErrorStart()
{
	CString strDatePath;
	CTime t = CTime::GetCurrentTime();
	strDatePath.Format("D:\\LOG_NEW\\%04d-%02d-%02d\\", t.GetYear(), t.GetMonth(), t.GetDay());

	CreatePath(strDatePath.GetBuffer());
	strDatePath.ReleaseBuffer();

	CString strFile;
	strFile.Format("%s%04d-%02d-%02d.txt", strDatePath, t.GetYear(), t.GetMonth(), t.GetDay());

	EnterCriticalSection(&m_csDataCollection);

	CStdioFile sf;
	sf.Open(strFile, CFile::modeCreate | CFile::modeNoTruncate | CFile::modeReadWrite | CFile::shareDenyNone);
	sf.SeekToEnd();

	CDat::UpdateValue("正常生产", 0);
	CDat::UpdateValue("设备故障", 1);
	CDat::UpdateValue("运行暂停", 0);
	CDat::UpdateValue("待机状态", 0);

	CString sItem[] = {
		"正常生产",
		"运行暂停",
		"设备故障",
		"待机状态",
		"设备故障信息",
		"设备故障开始/结束状态",
	};

	CString strLog, sValue;

	for (int i=0; i<6; i++)
	{
		sValue = CDat::m_mapDataCollection[sItem[i]].pDat->Value();
		//sValue.Replace(" ", ",");
		//sValue.Replace(",", "，");
		//sValue.Replace("\n", ",");
		//sValue.Replace("\r", ",");
		sValue.Replace("\r\n", "，"); // 先处理Windows换行符
		sValue.Replace("\n", "，");   // 再处理Unix换行符
		sValue.Replace("\r", "，");   // 最后处理MacOS换行符

		// 2. 再处理其他需要替换的字符
		sValue.Replace(" ", "，");  // 空格替换为中文逗号
		sValue.Replace(",", "，");  // 英文逗号替换为中文逗号

		// 3. 可选：处理连续的分隔符
		sValue.Replace("，，", "，"); // 替换连续的中文逗号
		strLog.Format("%s,%s,%s,%04d-%02d-%02d %02d:%02d:%02d,0,0\n", CDat::m_mapDataCollection[sItem[i]].sDataCode, sItem[i], sValue, t.GetYear(), t.GetMonth(), t.GetDay(), t.GetHour(), t.GetMinute(), t.GetSecond());
		sf.WriteString(strLog);
		sf.SeekToEnd();
	}

	sf.WriteString("\n");

	sf.Flush();
	sf.Close();

	LeaveCriticalSection(&m_csDataCollection);
}

void CDat::UpdateValueErrorEnd()
{
	CString strDatePath;
	CTime t = CTime::GetCurrentTime();
	strDatePath.Format("D:\\LOG_NEW\\%04d-%02d-%02d\\", t.GetYear(), t.GetMonth(), t.GetDay());

	CreatePath(strDatePath.GetBuffer());
	strDatePath.ReleaseBuffer();

	CString strFile;
	strFile.Format("%s%04d-%02d-%02d.txt", strDatePath, t.GetYear(), t.GetMonth(), t.GetDay());

	EnterCriticalSection(&m_csDataCollection);

	CStdioFile sf;
	sf.Open(strFile, CFile::modeCreate | CFile::modeNoTruncate | CFile::modeReadWrite | CFile::shareDenyNone);
	sf.SeekToEnd();

	CDat::UpdateValue("正常生产", 0);
	CDat::UpdateValue("设备故障", 0);
	CDat::UpdateValue("运行暂停", 0);
	CDat::UpdateValue("待机状态", 0);

	if (CSys::m_emRunStatus == emRunStatusRunning) {
		if (g_bWaitFlag) {
			CDat::UpdateValue("待机状态", 1);
		}
		else {
			CDat::UpdateValue("正常生产", 1);
		}
	}
	else if (CSys::m_emRunStatus == emRunStatusSuspending) {
		if (g_nWarnTimes > 0) {
			CDat::UpdateValue("设备故障", 1);
		}
		else {
			CDat::UpdateValue("运行暂停", 1);
		}
	}
	else {
		CDat::UpdateValue("待机状态", 1);
	}

	CString sItem[] = {
		"正常生产",
		"运行暂停",
		"设备故障",
		"待机状态",
		"设备故障信息",
		"设备故障开始/结束状态",
	};

	CString strLog, sValue;

	for (int i=0; i<6; i++)
	{
		sValue = CDat::m_mapDataCollection[sItem[i]].pDat->Value();
		//sValue.Replace(" ", "，");
		//sValue.Replace(",", "，");
		//sValue.Replace("\n", ",");
		//sValue.Replace("\r", ",");
		
		// 1. 先处理换行符，统一替换为英文逗号
		sValue.Replace("\r\n", "，"); // 先处理Windows换行符
		sValue.Replace("\n", "，");   // 再处理Unix换行符
		sValue.Replace("\r", "，");   // 最后处理MacOS换行符

		// 2. 再处理其他需要替换的字符
		sValue.Replace(" ", "，");  // 空格替换为中文逗号
		sValue.Replace(",", "，");  // 英文逗号替换为中文逗号

		// 3. 可选：处理连续的分隔符
		sValue.Replace("，，", "，"); // 替换连续的中文逗号
		strLog.Format("%s,%s,%s,%04d-%02d-%02d %02d:%02d:%02d,0,0\n", CDat::m_mapDataCollection[sItem[i]].sDataCode, sItem[i], sValue, t.GetYear(), t.GetMonth(), t.GetDay(), t.GetHour(), t.GetMinute(), t.GetSecond());
		
		sf.WriteString(strLog);
		sf.SeekToEnd();
	}

	sf.WriteString("\n");

	sf.Flush();
	sf.Close();

	LeaveCriticalSection(&m_csDataCollection);
}

void CDat::ClearOutdateDataCollection(int nOutDay)
{
	CString strDate;
	CTime t = CTime::GetCurrentTime();

	CTime tToday(t.GetYear(), t.GetMonth(), t.GetDay(), 0, 0, 0);

	int nRecordDay = 0, nRecordMonth = 0, nRecordYear = 0;

	CString strFile = "D:\\LOG_NEW\\";

	CString strFileName, strPathName;

	CFileFind ff;
	BOOL bFlag = ff.FindFile(strFile + "*.*");
	while (bFlag) {
		bFlag = ff.FindNextFile();

		if (ff.IsDirectory()) {
			strFileName = ff.GetFileName();
			if (strFileName.GetLength() < 10) {
				continue;
			}

			nRecordDay = atoi(strFileName.Mid(8, 2));
			nRecordMonth = atoi(strFileName.Mid(5, 2));
			nRecordYear = atoi(strFileName.Mid(0, 4));

			CTime tRecord(nRecordYear, nRecordMonth,nRecordDay, 0, 0, 0);
			CTimeSpan tSpan = tToday - tRecord;
			ULONGLONG nDays = tSpan.GetDays();

			if (nDays > nOutDay) {
				strPathName = ff.GetFilePath();
				DeleteDir(strPathName.GetBuffer());
				strPathName.ReleaseBuffer();
			}
		}
	}
}
